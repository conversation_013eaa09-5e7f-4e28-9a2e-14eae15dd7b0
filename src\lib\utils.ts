import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Utility functions for the application
 */

/**
 * Safely serialize objects that might contain BigInt values to JSON
 * This is necessary because JSON.stringify cannot natively handle BigInt values
 * 
 * @param data Any data structure that may contain BigInt values
 * @returns A copy of the data with BigInt values converted to strings
 */
export function serializeBigInt(data: any): any {
  return JSON.parse(JSON.stringify(data, (key, value) => 
    typeof value === 'bigint' ? value.toString() : value
  ));
}

/**
 * Format a currency value for display
 * @param amount Amount in cents
 * @param currency Currency code (default: USD)
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number | undefined, currency: string = 'USD'): string {
  if (amount === undefined) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2
  }).format(amount / 100);
}

/**
 * Format a date for display
 * @param dateString ISO date string
 * @param options Formatting options
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string | undefined | null,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  }
): string {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', options);
}

/**
 * Format a time for display
 * @param dateString ISO date string
 * @returns Formatted time string
 */
export function formatTime(dateString: string | undefined | null): string {
  if (!dateString) return '';
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}
