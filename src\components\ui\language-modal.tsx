"use client";

import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';

export function LanguageModal() {
  const { showLanguageModal, setShowLanguageModal, setLanguage, t } = useLanguage();

  const handleLanguageSelect = (language: 'en' | 'es') => {
    setLanguage(language);
    setShowLanguageModal(false);
  };

  return (
    <Dialog open={showLanguageModal} onOpenChange={setShowLanguageModal}>
      <DialogContent className="sm:max-w-md bg-white/90 backdrop-blur-sm border-[#8b5d53]/20">
        <DialogHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-[#8b5d53]/10 rounded-full flex items-center justify-center mb-4">
            <Globe className="h-6 w-6 text-[#8b5d53]" />
          </div>
          <DialogTitle className="text-xl text-[#5d3f39] font-light tracking-wide uppercase font-serif">
            {t('language.title')}
          </DialogTitle>
          <DialogDescription className="text-[#5d3f39]/80 font-light">
            {t('language.subtitle')}
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 py-4">
          <Button
            variant="outline"
            onClick={() => handleLanguageSelect('en')}
            className="h-24 flex flex-col items-center justify-center gap-2 border-[#8b5d53]/20 hover:bg-[#8b5d53]/10 hover:text-[#5d3f39] hover:border-[#8b5d53]/30"
          >
            <span className="text-2xl">🇺🇸</span>
            <span className="font-medium text-[#5d3f39]">{t('language.english')}</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => handleLanguageSelect('es')}
            className="h-24 flex flex-col items-center justify-center gap-2 border-[#8b5d53]/20 hover:bg-[#8b5d53]/10 hover:text-[#5d3f39] hover:border-[#8b5d53]/30"
          >
            <span className="text-2xl">🇲🇽</span>
            <span className="font-medium text-[#5d3f39]">{t('language.spanish')}</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
