"use client";

import React from "react";
import Link from "next/link";
import { 
  ArrowRightLeft,
  BarChart4,
  Calendar,
  CheckCircle2,
  Home,
  LayoutDashboard,
  Settings,
  Users
} from "lucide-react";
import { usePathname } from "next/navigation";

interface SidebarNavItem {
  name: string;
  href: string;
  icon: React.ElementType;
}

export function AdminSidebar() {
  const pathname = usePathname();
  
  const navItems: SidebarNavItem[] = [
    { name: "Dashboard", href: "/admin", icon: LayoutDashboard },
    { name: "Appointments", href: "/admin/appointments", icon: Calendar },
    { name: "Clients", href: "/admin/clients", icon: Users },
    { name: "Services", href: "/admin/services", icon: CheckCircle2 },
    { name: "Analytics", href: "/admin/analytics", icon: BarChart4 },
    { name: "Transactions", href: "/admin/transactions", icon: ArrowRightLeft },
    { name: "Settings", href: "/admin/settings", icon: Settings },
  ];
  
  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-stone-200">
        <div className="px-4 py-4 text-center">
          <h1 className="font-bold text-xl text-stone-800">GlowByBry</h1>
          <p className="text-sm text-stone-500">Admin Dashboard</p>
        </div>
        
        <div className="mt-6 px-3">
          <div className="space-y-1">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              
              return (
                <Link 
                  key={item.name}
                  href={item.href} 
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md 
                    ${isActive 
                      ? "bg-amber-100 text-amber-700" 
                      : "text-stone-700 hover:bg-stone-100"
                    }`}
                >
                  <item.icon className="mr-2 h-5 w-5" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        </div>
        
        <div className="mt-auto p-4 border-t border-stone-200">
          <Link href="/" className="flex items-center text-sm text-stone-600 hover:text-amber-600">
            <Home className="mr-2 h-4 w-4" />
            Back to Website
          </Link>
        </div>
      </div>
    </div>
  );
} 