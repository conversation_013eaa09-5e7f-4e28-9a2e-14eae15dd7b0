# GlowByBry
#

A modern, responsive SPA website for a beauty spa business. This project features a stunning 3D carousel, hero section, and service components.

## Features

- Animated 3D photo carousel
- Responsive hero section with parallax effects
- Modern UI components with animations
- Service showcase section

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Technologies Used

- [Next.js](https://nextjs.org)
- [Framer Motion](https://www.framer.com/motion/)
- [TailwindCSS](https://tailwindcss.com/)
- Modern React patterns and hooks

## Project Structure

- `/src/app` - Main application pages
- `/src/components/ui` - Reusable UI components including the 3D carousel
- `/public/images` - Spa and beauty images for the carousel

## Customization

You can customize the content by modifying the text in the `src/app/home/<USER>/images` directory.

## Created By

SolanaSergio + B