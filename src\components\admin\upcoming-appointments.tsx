"use client";

import React from "react";
import { Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AppointmentData {
  id: string;
  client: string;
  service: string;
  date: {
    day: number;
    month: string;
  };
  time: string;
  duration: string;
}

interface UpcomingAppointmentsProps {
  appointments: AppointmentData[];
  className?: string;
}

export function UpcomingAppointments({ appointments, className = "" }: UpcomingAppointmentsProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-stone-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-stone-800">Today&apos;s Appointments</h3>
        <Button variant="outline" size="sm">
          <Calendar className="mr-1 h-4 w-4" />
          View Calendar
        </Button>
      </div>

      <div className="space-y-3">
        {appointments.map((appointment) => (
          <div
            key={appointment.id}
            className="flex items-start p-3 bg-stone-50 rounded-lg"
          >
            <div className="flex-shrink-0 mr-3">
              <div className="text-center">
                <div className="text-xs font-medium text-stone-500">{appointment.date.month}</div>
                <div className="text-xl font-bold text-stone-800">{appointment.date.day}</div>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-stone-800">{appointment.client}</h4>
                <span className="text-xs font-medium text-amber-600">{appointment.time}</span>
              </div>
              <p className="text-xs text-stone-500 mt-1">{appointment.service} ({appointment.duration})</p>
            </div>
          </div>
        ))}
      </div>

      {appointments.length === 0 && (
        <div className="py-6 text-center text-stone-500">
          <p>No appointments scheduled for today</p>
        </div>
      )}
    </div>
  );
}