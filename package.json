{"name": "glowbybry", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "npm install critters@0.0.23 --no-save"}, "dependencies": {"@geist-ui/core": "2.3.8", "@getbrevo/brevo": "2.2.0", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-scroll-area": "1.2.8", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "1.1.11", "@tsparticles/engine": "3.8.1", "@tsparticles/react": "3.0.0", "@tsparticles/slim": "3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel-react": "^8.6.0", "framer-motion": "12.7.4", "lucide-react": "^0.487.0", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "square": "25.2.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "1.0.7", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "autoprefixer": "10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "8.5.3", "tailwindcss": "3.4.17", "typescript": "^5"}}