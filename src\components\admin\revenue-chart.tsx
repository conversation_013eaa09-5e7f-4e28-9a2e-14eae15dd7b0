"use client";

import React from "react";
import { Button } from "@/components/ui/button";

interface RevenueChartProps {
  className?: string;
}

export function RevenueChart({ className = "" }: RevenueChartProps) {
  const [chartView, setChartView] = React.useState<"weekly" | "monthly">("weekly");
  
  // This is a placeholder component. In a real application, you would integrate
  // a charting library like Chart.js, Recharts, or react-chartjs-2
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-stone-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-stone-800">Revenue Overview</h3>
        <div className="flex items-center space-x-2">
          <Button 
            size="sm" 
            variant="outline" 
            className={`text-sm ${chartView === "monthly" ? "bg-stone-100" : ""}`}
            onClick={() => setChartView("monthly")}
          >
            Monthly
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            className={`text-sm ${chartView === "weekly" ? "bg-stone-100" : ""}`}
            onClick={() => setChartView("weekly")}
          >
            Weekly
          </Button>
        </div>
      </div>
      
      <div className="h-80 flex flex-col items-center justify-center bg-stone-50 rounded-lg">
        <p className="text-stone-500 mb-2">Revenue chart will be displayed here</p>
        <p className="text-xs text-stone-400">Currently showing {chartView} data</p>
        
        {/* Chart placeholder - visual representation */}
        <div className="w-full max-w-md mt-4 px-8">
          <div className="flex items-end justify-between h-40">
            {[35, 55, 80, 65, 90, 75, 40].map((height, index) => (
              <div key={index} className="flex flex-col items-center">
                <div 
                  className="w-8 bg-amber-400 rounded-t-md transition-all duration-300"
                  style={{ height: `${height}%` }}
                ></div>
                <div className="text-xs text-stone-500 mt-2">
                  {chartView === "weekly" 
                    ? ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"][index]
                    : ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"][index]
                  }
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 