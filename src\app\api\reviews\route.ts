import { NextResponse } from 'next/server';
import { testimonials } from '@/data/testimonials';
import { reviewsConfig } from '@/config/reviews';

// This API endpoint handles Google reviews fetching
// It can be configured to use real Google Places API or return local testimonials

export const dynamic = 'force-dynamic'; // Mark this route as using dynamic features

// Enhanced filtering function specifically for <PERSON><PERSON>'s reviews
function filterReviewsForBryanna(reviews: any[], reviewReferences: string[] = []): any[] {
  console.log(`Looking for reviews with these references: [${reviewReferences.map(ref => `'${ref}'`).join(', ')}]`);
  console.log(`Total reviews to filter: ${reviews.length}`);
  
  const filteredReviews: any[] = [];
  
  reviews.forEach((review, index) => {
    const reviewNum = index + 1;
    const reviewText = review.text || review.content || '';
    const authorName = review.author_name || review.name || '';
    const reviewTime = review.time || Date.now() / 1000;
    
    console.log(`\n--- Review ${reviewNum} Analysis ---`);
    console.log(`Author: ${authorName}`);
    console.log(`Time: ${Math.floor(reviewTime)} (${new Date(reviewTime * 1000).toLocaleDateString()})`);
    console.log(`Text: "${reviewText}"`);
    console.log(`Rating: ${review.rating || 'N/A'}`);
    
    // Check for direct mentions of Bryanna/Bry
    const directBryannaMentions = [
      'bryanna', 'bry gave', 'bry is', 'bry was', 'bry did', 'bry has', 'bry always', 'bry provided',
      'bry ', ' bry', 'bryanna ', ' bryanna', 'bry!', 'bry.', 'bry,', 'bry\'s'
    ];
    const hasDirectMention = directBryannaMentions.some(mention => 
      reviewText.toLowerCase().includes(mention) || 
      authorName.toLowerCase().includes('bryanna') || 
      authorName.toLowerCase().includes('bry')
    );
    
    // Check for mentions of other staff (exclusion criteria)
    const otherStaffMentions = ['wesley', 'gracie', 'grace', 'wes'];
    const mentionsOtherStaff = otherStaffMentions.some(staff => 
      reviewText.toLowerCase().includes(staff.toLowerCase()) || 
      authorName.toLowerCase().includes(staff.toLowerCase())
    );
    
    // Check for services that Bryanna provides
    const bryannaServices = [
      'facial', 'facials', 'face', 'skin', 'massage', 'lymphatic', 'treatment', 'laser', 'hair removal',
      'waxing', 'brow', 'eyebrow', 'lash', 'lashes', 'beauty', 'spa', 'glow', 'professional', 'gentle',
      'skincare', 'acne', 'hydrafacial', 'microdermabrasion', 'chemical peel', 'dermaplaning'
    ];
    const mentionsServices = bryannaServices.some(service => 
      reviewText.toLowerCase().includes(service)
    );
    
    // Check for positive experience indicators
    const positiveIndicators = [
      'amazing', 'best', 'love', 'satisfied', 'recommend', 'professional', 'gentle', 
      'knowledgeable', 'talented', 'comfortable', 'clean', 'relaxing', 'great', 'fantastic',
      'wonderful', 'excellent', 'perfect', 'happy', 'pleased', 'awesome', 'incredible',
      'outstanding', 'phenomenal', 'superb', 'brilliant', 'fabulous'
    ];
    const hasPositiveIndicators = positiveIndicators.some(indicator => 
      reviewText.toLowerCase().includes(indicator)
    );
    
    console.log(`Direct Bryanna mention: ${hasDirectMention}`);
    console.log(`Mentions other staff: ${mentionsOtherStaff}`);
    console.log(`Mentions services: ${mentionsServices} (${bryannaServices.filter(service => reviewText.toLowerCase().includes(service)).join(', ')})`);
    console.log(`Has positive indicators: ${hasPositiveIndicators} (${positiveIndicators.filter(indicator => reviewText.toLowerCase().includes(indicator)).join(', ')})`);
    
    // Decision logic - more inclusive for Bryanna's reviews
    if (mentionsOtherStaff) {
      console.log(`❌ EXCLUDED: Mentions other staff (${otherStaffMentions.filter(staff => 
        reviewText.toLowerCase().includes(staff.toLowerCase()) || 
        authorName.toLowerCase().includes(staff.toLowerCase())
      ).join(', ')})`);
      return;
    }
    
    if (hasDirectMention) {
      console.log(`✅ INCLUDED: Directly mentions Bryanna/Bry`);
      filteredReviews.push(transformReview(review, index));
      return;
    }
    
    // Include reviews about services Bryanna provides (if no other staff mentioned)
    if (mentionsServices && !mentionsOtherStaff) {
      console.log(`✅ INCLUDED: About services Bryanna provides`);
      filteredReviews.push(transformReview(review, index));
      return;
    }
    
    // Include positive reviews about the business (if no other staff mentioned and has positive indicators)
    if (hasPositiveIndicators && !mentionsOtherStaff && reviewText.length > 30) {
      console.log(`✅ INCLUDED: Positive review about the business`);
      filteredReviews.push(transformReview(review, index));
      return;
    }
    
    // If we have specific review references and this review seems relevant, be more inclusive
    if (reviewReferences.length > 0 && (mentionsServices || hasPositiveIndicators) && !mentionsOtherStaff) {
      console.log(`✅ INCLUDED: Relevant review with specific references provided`);
      filteredReviews.push(transformReview(review, index));
      return;
    }
    
    console.log(`❌ EXCLUDED: Does not meet inclusion criteria`);
  });
  
  console.log(`\n=== FILTERING SUMMARY ===`);
  console.log(`Original reviews count: ${reviews.length}`);
  console.log(`Filtered to ${filteredReviews.length} reviews for Bryanna`);
  console.log(`Returning ${filteredReviews.length} reviews to client`);
  
  return filteredReviews;
}

// Helper function to transform review to our format
function transformReview(review: any, index: number): any {
  // Handle Google API format
  if (review.author_name || review.text) {
    return {
      id: review.id || `google-review-${index}`,
      name: review.author_name || 'Anonymous',
      role: 'Google Reviewer',
      content: review.text || '',
      image: review.profile_photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(review.author_name || 'Anonymous')}&background=8b5d53&color=fff`,
      rating: review.rating || 5,
      date: review.time ? new Date(review.time * 1000).toISOString() : new Date().toISOString(),
      source: 'google' as const
    };
  }
  
  // Handle local testimonial format
  return {
    id: review.id || `local-review-${index}`,
    name: review.name || 'Anonymous',
    role: review.role || 'Client',
    content: review.content || '',
    image: review.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(review.name || 'Anonymous')}&background=8b5d53&color=fff`,
    rating: review.rating || 5,
    date: review.date || new Date().toISOString(),
    source: review.source || 'local' as const
  };
}

// GET endpoint - returns local testimonials
export async function GET() {
  try {
    // In a real implementation, you would:
    // 1. Use Google's Place API to fetch reviews
    // 2. Cache the results for better performance
    // 3. Handle rate limiting and API quotas
    
    // For now, return the local testimonials with Google source
    const googleFormattedReviews = testimonials.map(review => ({
      ...review,
      source: 'local' as const,
      date: new Date().toISOString()
    }));
    
    return NextResponse.json({ 
      success: true, 
      reviews: googleFormattedReviews,
      source: 'local'
    });
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch reviews' },
      { status: 500 }
    );
  }
}

// POST endpoint - handles URL processing and configuration
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { urls = [], config = {}, useRealAPI = false } = body;
    
    // Process URLs to extract place IDs and coordinates first
    const processedUrls = urls.map((url: string) => {
      // Handle shortened Google Maps URLs (like maps.app.goo.gl)
      if (url.includes('maps.app.goo.gl') || url.includes('goo.gl')) {
        console.log('Detected shortened Google Maps URL:', url);
        return {
          url,
          placeId: null,
          coordinates: null,
          placeReference: null,
          reviewReference: null,
          isShortened: true
        };
      }
      
      // Handle direct Google Maps review URLs
      if (url.includes('maps/reviews/') || url.includes('/reviews/@')) {
        console.log('Detected Google Maps review URL:', url);
        
        // Extract coordinates from review URLs
        const coordMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
        
        // Extract review reference from the URL
        const reviewRefMatch = url.match(/1s([a-zA-Z0-9_-]+)!/);
        
        // Extract place reference from hex format
        const placeHexMatch = url.match(/1s0x[a-fA-F0-9]+:0x([a-fA-F0-9]+)/);
        
        return {
          url,
          placeId: null,
          coordinates: coordMatch ? { lat: coordMatch[1], lng: coordMatch[2] } : null,
          placeReference: placeHexMatch ? placeHexMatch[1] : null,
          reviewReference: reviewRefMatch ? reviewRefMatch[1] : null,
          isReviewUrl: true
        };
      }
      
      // Extract place ID from Google Maps URLs
      const placeIdMatch = url.match(/place_id=([a-zA-Z0-9_-]+)/);
      
      // Extract coordinates from Google Maps URLs
      const coordMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
      
      // Extract place reference from review URLs
      const placeRefMatch = url.match(/1s([a-zA-Z0-9_-]+)!/);
      
      // Extract hex place ID format
      const hexPlaceMatch = url.match(/0x([a-fA-F0-9]+):0x([a-fA-F0-9]+)/);
      
      return {
        url,
        placeId: placeIdMatch ? placeIdMatch[1] : null,
        coordinates: coordMatch ? { lat: coordMatch[1], lng: coordMatch[2] } : null,
        placeReference: placeRefMatch ? placeRefMatch[1] : null,
        reviewReference: null,
        hexPlaceId: hexPlaceMatch ? `0x${hexPlaceMatch[1]}:0x${hexPlaceMatch[2]}` : null,
        isShortened: false
      };
    });
    
    console.log('Processed URLs:', processedUrls);
    
    // Extract review references for enhanced filtering
    const reviewReferences = processedUrls
      .flatMap((url: any) => [url.placeReference, url.reviewReference])
      .filter((ref: string | null) => ref !== null);
    
    console.log('Review references found:', reviewReferences);
    
    // If we have URLs with coordinates and API key, try to find the business first
    if (urls.length > 0 && config.apiKey) {
      // Handle shortened URLs by searching for business name
      const hasShortened = processedUrls.some((url: any) => url.isShortened);
      if (hasShortened) {
        console.log('Processing shortened Google Maps URL - searching for business by name...');
        
        // Search for "Beauty Health and Wellness" where Bryanna works
        const businessNames = [
          'Beauty Health and Wellness La Quinta CA',
          'Beauty Health & Wellness La Quinta',
          'Beauty Health and Wellness',
          'Beauty Health & Wellness'
        ];
        
        for (const businessName of businessNames) {
          try {
            console.log(`Searching for business: "${businessName}"`);
            
            // Use text search to find the business
            const searchResponse = await fetch(
              `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(businessName)}&key=${config.apiKey}`
            );
            
            if (searchResponse.ok) {
              const searchData = await searchResponse.json();
              console.log(`Search results for "${businessName}":`, searchData);
              
              if (searchData.results && searchData.results.length > 0) {
                for (const place of searchData.results) {
                  console.log(`Found business: ${place.name} (Place ID: ${place.place_id})`);
                  
                  try {
                    const rawReviews = await fetchGooglePlacesReviews(config.apiKey, place.place_id);
                    if (rawReviews.length > 0) {
                      console.log(`Found ${rawReviews.length} raw reviews for ${place.name}`);
                      
                      // Apply enhanced Bryanna filtering
                      const filteredReviews = filterReviewsForBryanna(rawReviews, []);
                      
                      if (filteredReviews.length > 0) {
                        const hasRealGoogleReviews = filteredReviews.some((review: any) => review.source === 'google');
                        const source = hasRealGoogleReviews ? 'google' : 'local';
                        const totalReviews = place.user_ratings_total || 'Unknown';
                        const returnedReviews = rawReviews.length;
                        const message = hasRealGoogleReviews 
                          ? `Found ${filteredReviews.length} Bryanna reviews from "${place.name}" (${returnedReviews} of ${totalReviews} total reviews checked - Google API limitation)`
                          : `Showing ${filteredReviews.length} local testimonials for Bryanna`;
                        
                        return NextResponse.json({
                          success: true,
                          reviews: filteredReviews,
                          source: source,
                          requestedUrls: urls,
                          processedUrls,
                          foundBusiness: place.name,
                          foundPlaceId: place.place_id,
                          searchMethod: 'business_name_search',
                          totalReviewsInBusiness: totalReviews,
                          reviewsReturnedByAPI: returnedReviews,
                          apiLimitation: 'Google Places API only returns 5 most recent reviews',
                          message: message
                        });
                      } else {
                        console.log(`No Bryanna-specific reviews found for ${place.name} after filtering`);
                      }
                    }
                  } catch (reviewError) {
                    console.log(`Error fetching reviews for ${place.name}:`, reviewError);
                  }
                }
              }
            }
          } catch (searchError) {
            console.log(`Error searching for business "${businessName}":`, searchError);
          }
        }
        
        // If business name search didn't work, try the specific Place ID for Beauty Health and Wellness
        console.log('Business name search failed, trying direct Place ID lookup...');
        const beautyHealthPlaceId = 'EjI3NzkzNSBDYWxsZSBUYW1waWNvICMxMDMsIExhIFF1aW50YSwgQ0EgOTIyNTMsIFVTQSIfGh0KFgoUChIJVwMFN7z42oARYiORN8FjBWMSAzEwMw';
        
        try {
          console.log(`Trying direct Place ID lookup for Beauty Health and Wellness: ${beautyHealthPlaceId}`);
          const rawReviews = await fetchGooglePlacesReviews(config.apiKey, beautyHealthPlaceId);
          if (rawReviews.length > 0) {
            console.log(`Found ${rawReviews.length} raw reviews for Beauty Health and Wellness`);
            
            // Apply enhanced Bryanna filtering
            const filteredReviews = filterReviewsForBryanna(rawReviews, []);
            
            if (filteredReviews.length > 0) {
              const hasRealGoogleReviews = filteredReviews.some((review: any) => review.source === 'google');
              const source = hasRealGoogleReviews ? 'google' : 'local';
              const message = hasRealGoogleReviews 
                ? `Found ${filteredReviews.length} reviews for Bryanna from Beauty Health and Wellness`
                : `Showing ${filteredReviews.length} local testimonials for Bryanna`;
              
              return NextResponse.json({
                success: true,
                reviews: filteredReviews,
                source: source,
                requestedUrls: urls,
                processedUrls,
                foundBusiness: 'Beauty Health and Wellness',
                foundPlaceId: beautyHealthPlaceId,
                searchMethod: 'direct_place_id',
                message: message
              });
            }
          }
        } catch (placeIdError) {
          console.log(`Error with direct Place ID lookup:`, placeIdError);
        }
      }
      
      // Original coordinate-based search logic
      for (const processedUrl of processedUrls) {
        if (processedUrl.coordinates) {
          try {
            console.log(`Searching for business at coordinates: ${processedUrl.coordinates.lat}, ${processedUrl.coordinates.lng}`);
            
            // Try nearby search for beauty salons, spas, etc.
            const nearbyResponse = await fetch(
              `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${processedUrl.coordinates.lat},${processedUrl.coordinates.lng}&radius=100&type=beauty_salon&key=${config.apiKey}`
            );
            
            if (nearbyResponse.ok) {
              const nearbyData = await nearbyResponse.json();
              console.log('Nearby beauty salons:', nearbyData);
              
              if (nearbyData.results && nearbyData.results.length > 0) {
                // Try each nearby business to find one with reviews
                for (const place of nearbyData.results) {
                  console.log(`Checking business: ${place.name} (Place ID: ${place.place_id})`);
                  
                  try {
                    const rawReviews = await fetchGooglePlacesReviews(config.apiKey, place.place_id);
                    if (rawReviews.length > 0) {
                      // Apply enhanced Bryanna filtering
                      const filteredReviews = filterReviewsForBryanna(rawReviews, reviewReferences);
                      
                      if (filteredReviews.length > 0) {
                        const hasRealGoogleReviews = filteredReviews.some((review: any) => review.source === 'google');
                        const source = hasRealGoogleReviews ? 'google' : 'local';
                        const message = hasRealGoogleReviews 
                          ? `Found ${filteredReviews.length} reviews for Bryanna from "${place.name}"`
                          : `Showing ${filteredReviews.length} local testimonials for Bryanna`;
                        
                        return NextResponse.json({
                          success: true,
                          reviews: filteredReviews,
                          source: source,
                          requestedUrls: urls,
                          processedUrls,
                          foundBusiness: place.name,
                          foundPlaceId: place.place_id,
                          message: message
                        });
                      }
                    }
                  } catch (reviewError) {
                    console.log(`No reviews found for ${place.name}:`, reviewError);
                  }
                }
              }
            }
            
            // If no beauty salons found, try general nearby search
            const generalNearbyResponse = await fetch(
              `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${processedUrl.coordinates.lat},${processedUrl.coordinates.lng}&radius=100&key=${config.apiKey}`
            );
            
            if (generalNearbyResponse.ok) {
              const generalNearbyData = await generalNearbyResponse.json();
              console.log('All nearby businesses:', generalNearbyData);
              
              if (generalNearbyData.results && generalNearbyData.results.length > 0) {
                // Try each nearby business to find one with reviews
                for (const place of generalNearbyData.results) {
                  // Skip residential addresses
                  if (place.types.includes('subpremise') || place.types.includes('street_address')) {
                    continue;
                  }
                  
                  console.log(`Checking business: ${place.name} (Place ID: ${place.place_id})`);
                  
                  try {
                    const rawReviews = await fetchGooglePlacesReviews(config.apiKey, place.place_id);
                    if (rawReviews.length > 0) {
                      // Apply enhanced Bryanna filtering
                      const filteredReviews = filterReviewsForBryanna(rawReviews, reviewReferences);
                      
                      if (filteredReviews.length > 0) {
                        const hasRealGoogleReviews = filteredReviews.some((review: any) => review.source === 'google');
                        const source = hasRealGoogleReviews ? 'google' : 'local';
                        const message = hasRealGoogleReviews 
                          ? `Found ${filteredReviews.length} reviews for Bryanna from "${place.name}"`
                          : `Showing ${filteredReviews.length} local testimonials for Bryanna`;
                        
                        return NextResponse.json({
                          success: true,
                          reviews: filteredReviews,
                          source: source,
                          requestedUrls: urls,
                          processedUrls,
                          foundBusiness: place.name,
                          foundPlaceId: place.place_id,
                          message: message
                        });
                      }
                    }
                  } catch (reviewError) {
                    console.log(`No reviews found for ${place.name}:`, reviewError);
                  }
                }
              }
            }
          } catch (error) {
            console.log('Error in business search:', error);
          }
        }
      }
    }
    
    // If Google Places API configuration is provided and we want to use real API (fallback)
    if (config.apiKey && config.placeId && useRealAPI) {
      try {
        console.log('Fallback: Fetching real Google reviews for configured Place ID:', config.placeId);
        const rawReviews = await fetchGooglePlacesReviews(config.apiKey, config.placeId);
        
        // Apply enhanced Bryanna filtering
        const filteredReviews = filterReviewsForBryanna(rawReviews, reviewReferences);
        
        // Check if we got real Google reviews or fallback testimonials
        const hasRealGoogleReviews = filteredReviews.some((review: any) => review.source === 'google');
        const source = hasRealGoogleReviews ? 'google' : 'local';
        const message = hasRealGoogleReviews 
          ? `Found ${filteredReviews.length} reviews for Bryanna from configured Place ID`
          : `Showing ${filteredReviews.length} local testimonials for Bryanna`;
        
        return NextResponse.json({
          success: true,
          reviews: filteredReviews,
          source: source,
          requestedUrls: urls,
          message: message
        });
      } catch (apiError) {
        console.error('Google Places API error:', apiError);
        
        // Return error details for debugging
        return NextResponse.json({
          success: false,
          error: `Google Places API error: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`,
          fallbackUsed: false
        }, { status: 400 });
      }
    }
    
    // For now, return local testimonials with URL processing info
    const googleFormattedReviews = testimonials.map(review => ({
      ...review,
      source: 'local' as const,
      date: new Date().toISOString()
    }));
    
    // Apply filtering to local testimonials too
    const filteredLocalReviews = filterReviewsForBryanna(googleFormattedReviews, reviewReferences);
    
    return NextResponse.json({ 
      success: true, 
      reviews: filteredLocalReviews,
      source: 'local',
      requestedUrls: urls,
      processedUrls,
      message: 'Using local testimonials. Configure Google Places API for real reviews.'
    });
  } catch (error) {
    console.error('Error processing review request:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process review request' },
      { status: 500 }
    );
  }
}

// Helper function to fetch reviews from Google Places API
async function fetchGooglePlacesReviews(apiKey: string, placeId: string) {
  try {
    console.log('Making Google Places API request...');
    
    // First, try with specific fields
    let response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=reviews,rating,user_ratings_total,name&key=${apiKey}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      }
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    let data = await response.json();
    console.log('Google Places API response status:', data.status);
    console.log('Business name:', data.result?.name);
    console.log('Overall rating:', data.result?.rating);
    console.log('Total ratings:', data.result?.user_ratings_total);
    console.log(`⚠️  IMPORTANT: Google Places API Limitation`);
    console.log(`📊 Total reviews for this business: ${data.result?.user_ratings_total || 'Unknown'}`);
    console.log(`📥 Reviews returned by API: ${data.result?.reviews?.length || 0}`);
    console.log(`🔒 Google Places API only returns the 5 most recent reviews, not all reviews`);
    console.log('Full API response:', JSON.stringify(data, null, 2));
    
    if (data.status !== 'OK') {
      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`);
    }
    
    if (!data.result) {
      console.log('No result object in API response');
      return [];
    }
    
    // If no reviews with specific fields, try requesting ALL fields
    if (!data.result.reviews) {
      console.log('No reviews with specific fields, trying ALL fields...');
      
      response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${apiKey}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        }
      );
      
      if (response.ok) {
        data = await response.json();
        console.log('ALL fields API response:', JSON.stringify(data, null, 2));
      }
    }
    
    if (!data.result.reviews) {
      console.log('No reviews array in API response - this could mean:');
      console.log('1. Business has no reviews yet');
      console.log('2. Reviews are not publicly accessible via API');
      console.log('3. Business profile needs to be verified');
      console.log('4. Reviews exist but API doesn\'t return them (common limitation)');
      console.log('Available fields in result:', Object.keys(data.result));
      console.log('');
      console.log('ALTERNATIVE SOLUTIONS:');
      console.log('1. Use Google My Business API (requires business verification)');
      console.log('2. Use third-party review aggregation services');
      console.log('3. Manually add reviews to local testimonials');
      console.log('');
      console.log('Falling back to local testimonials...');
      
      // Return local testimonials as fallback, but mark them as local source
      const fallbackReviews = testimonials.map((review, index) => ({
        id: `fallback-${placeId}-${index}`,
        name: review.name,
        role: review.role,
        content: review.content,
        image: review.image,
        rating: review.rating,
        date: new Date().toISOString(),
        source: 'local' as const,
        // Add fields that match Google API format for consistency
        author_name: review.name,
        text: review.content,
        time: Math.floor(Date.now() / 1000)
      }));
      
      return fallbackReviews;
    }
    
    console.log(`Found ${data.result.reviews.length} reviews from Google Places API`);
    
    // Return raw reviews without filtering - filtering will be done by filterReviewsForBryanna
    return data.result.reviews;
  } catch (error) {
    console.error('Error in fetchGooglePlacesReviews:', error);
    throw error;
  }
} 