import { NextResponse } from 'next/server';
import { bookingsApi, catalogApi, locationsApi } from '../config';
import { serializeBigInt } from '@/lib/utils';

export async function GET() {
  try {
    const debugInfo: any = {
      location: null,
      services: [],
      businessHours: null,
      errors: []
    };

    // 1. Check location
    try {
      const { result: locationResult } = await locationsApi.retrieveLocation(
        process.env.SQUARE_LOCATION_ID!
      );
      debugInfo.location = {
        id: locationResult.location?.id,
        name: locationResult.location?.businessName,
        status: locationResult.location?.status,
        businessHours: locationResult.location?.businessHours,
      };
    } catch (error) {
      debugInfo.errors.push({
        component: 'location',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 2. Check services
    try {
      const { result: catalogResult } = await catalogApi.searchCatalogItems({
        productTypes: ["APPOINTMENTS_SERVICE"]
      });

      debugInfo.services = catalogResult.items?.map(item => ({
        id: item.id,
        name: item.itemData?.name,
        variations: item.itemData?.variations?.map(variation => ({
          id: variation.id,
          name: variation.itemVariationData?.name,
          serviceDuration: variation.itemVariationData?.serviceDuration,
          price: variation.itemVariationData?.priceMoney?.amount
        }))
      }));
    } catch (error) {
      debugInfo.errors.push({
        component: 'services',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // 3. Check availability for next 7 days
    try {
      const startAt = new Date();
      const endAt = new Date();
      endAt.setDate(endAt.getDate() + 7);

      const { result: availabilityResult } = await bookingsApi.searchAvailability({
        query: {
          filter: {
            startAtRange: {
              startAt: startAt.toISOString(),
              endAt: endAt.toISOString()
            },
            locationId: process.env.SQUARE_LOCATION_ID,
          }
        }
      });

      const availabilities = availabilityResult.availabilities || [];
      debugInfo.availability = {
        hasSlots: availabilities.length > 0,
        totalSlots: availabilities.length,
        sampleSlots: availabilities.slice(0, 3)
      };
    } catch (error) {
      debugInfo.errors.push({
        component: 'availability',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Use the utility function to safely serialize the data
    const safeDebugInfo = serializeBigInt(debugInfo);

    return NextResponse.json({
      status: 'success',
      debug: safeDebugInfo
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to gather debug information',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 