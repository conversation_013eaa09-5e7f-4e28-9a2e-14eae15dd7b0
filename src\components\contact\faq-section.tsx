"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { HelpCircle } from "lucide-react";
import { Accordion, AccordionItem } from "@/components/ui/accordion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function FAQSection() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <Card className="border-[#8b5d53]/20 shadow-md bg-white/80 backdrop-blur-sm overflow-hidden">
        <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-3 px-4 md:py-4 md:px-6">
          <div className="flex items-center gap-3">
            <HelpCircle className="h-5 w-5 text-[#8b5d53]" />
            <CardTitle className="text-xl text-[#5d3f39] font-light tracking-wide uppercase font-serif">Frequently Asked Questions</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-4 md:p-6">
          <Accordion>
            <AccordionItem title="How do I book an appointment?">
              <p className="text-[#5d3f39]/80 font-light leading-relaxed">
                You can book an appointment through our website by visiting the Booking page,
                calling us directly at (951) 233-9363, or by sending us a message through the contact form above.
                We recommend booking at least 48 hours in advance to secure your preferred time slot.
              </p>
            </AccordionItem>

            <AccordionItem title="What are your hours of operation?">
              <p className="text-[#5d3f39]/80 font-light leading-relaxed">
                We&apos;re open Monday through Friday from 10:00 AM to 5:00 PM, and Saturday from 10:00 AM to 3:00 PM.
                We&apos;re closed on Sundays. For appointments outside these hours, please contact us to discuss availability.
              </p>
            </AccordionItem>

            <AccordionItem title="How quickly will you respond to my message?">
              <p className="text-[#5d3f39]/80 font-light leading-relaxed">
                We typically respond to all inquiries within 24 hours during business days.
                For urgent matters or same-day appointments, we recommend calling us directly at (951) 233-9363.
              </p>
            </AccordionItem>

            <AccordionItem title="Do you offer consultations?">
              <p className="text-[#5d3f39]/80 font-light leading-relaxed">
                Yes! We offer complimentary consultations to discuss your skin goals and recommend the best treatments for you.
                You can schedule a consultation through our booking system or mention it in your contact form message.
              </p>
            </AccordionItem>

            <AccordionItem title="What should I expect during my first visit?">
              <p className="text-[#5d3f39]/80 font-light leading-relaxed">
                During your first visit, we&apos;ll conduct a thorough skin analysis and discuss your goals and concerns.
                Please arrive 10-15 minutes early to complete intake forms. We&apos;ll create a personalized treatment plan just for you.
              </p>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </motion.div>
  );
}
