/**
 * Date and Time Utilities for Booking System
 */

/**
 * Check if a date is in the past
 * @param date Date to check
 * @returns Boolean indicating if the date is in the past
 */
export function isDateInPast(date: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  date.setHours(0, 0, 0, 0);
  return date < today;
}

/**
 * Check if a date is a valid booking date
 * @param date Date to check
 * @param excludeWeekends Whether to exclude weekends
 * @returns Boolean indicating if the date is valid for booking
 */
export function isValidBookingDate(date: Date, excludeWeekends: boolean = true): boolean {
  // Check if date is in the past
  if (isDateInPast(date)) {
    return false;
  }
  
  // Check if date is a weekend (0 = Sunday, 6 = Saturday)
  if (excludeWeekends && (date.getDay() === 0 || date.getDay() === 6)) {
    return false;
  }
  
  return true;
}

/**
 * Get available time slots for a given date
 * @param date Date to get time slots for
 * @param duration Duration of the appointment in minutes
 * @param startTime Start time of the business day (24-hour format)
 * @param endTime End time of the business day (24-hour format)
 * @param interval Interval between time slots in minutes
 * @returns Array of available time slots in 12-hour format
 */
export function getAvailableTimeSlots(
  date: Date,
  duration: number = 60,
  startTime: string = "09:00",
  endTime: string = "17:00",
  interval: number = 60
): string[] {
  const timeSlots: string[] = [];
  const today = new Date();
  const isToday = date.toDateString() === today.toDateString();
  
  // Parse start and end times
  const [startHour, startMinute] = startTime.split(":").map(Number);
  const [endHour, endMinute] = endTime.split(":").map(Number);
  
  // Convert to minutes for easier calculation
  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;
  
  // If it's today, adjust start time to next available slot
  let currentMinutes = startMinutes;
  if (isToday) {
    const currentHour = today.getHours();
    const currentMinute = today.getMinutes();
    const nowMinutes = currentHour * 60 + currentMinute;
    
    // Round up to the next interval
    currentMinutes = Math.ceil(nowMinutes / interval) * interval;
    
    // Add buffer time (e.g., 1 hour)
    currentMinutes += 60;
    
    // If current time + buffer is past business hours, return empty array
    if (currentMinutes >= endMinutes) {
      return [];
    }
  }
  
  // Generate time slots
  while (currentMinutes + duration <= endMinutes) {
    const hour = Math.floor(currentMinutes / 60);
    const minute = currentMinutes % 60;
    
    // Convert to 12-hour format
    const period = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 || 12;
    const displayMinute = minute.toString().padStart(2, "0");
    
    timeSlots.push(`${displayHour}:${displayMinute} ${period}`);
    
    // Move to next slot
    currentMinutes += interval;
  }
  
  return timeSlots;
}

/**
 * Format a date for display
 * @param dateString Date string to format
 * @param options Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string,
  options: Intl.DateTimeFormatOptions = { 
    weekday: 'long', 
    month: 'long', 
    day: 'numeric', 
    year: 'numeric' 
  }
): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', options);
}

/**
 * Get the next available date for booking
 * @param excludeWeekends Whether to exclude weekends
 * @returns Next available date
 */
export function getNextAvailableDate(excludeWeekends: boolean = true): Date {
  const date = new Date();
  date.setDate(date.getDate() + 1); // Start with tomorrow
  
  // Keep incrementing until we find a valid date
  while (!isValidBookingDate(date, excludeWeekends)) {
    date.setDate(date.getDate() + 1);
  }
  
  return date;
}
