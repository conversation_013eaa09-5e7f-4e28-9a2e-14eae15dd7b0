"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { BeamsBackground } from "@/components/ui/beams-background";
import { SparklesCore } from "@/components/ui/sparkles";

import {
  Check,
  ArrowRight,
  ArrowLeft,
  Clock,
  Loader
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { getAvailableTimeSlots, formatDate as formatDateUtil, getNextAvailableDate } from "@/lib/dateUtils";



interface Service {
  id: string;
  name: string;
  description: string;
  duration: string;
  price: number | string;
}

interface SquareAvailabilitySlot {
  startAt: string;
}

// Default service options
const defaultServices: Service[] = [
  {
    id: "facial-1",
    name: "Customized Facials",
    description: "Revitalize your skin with this deeply hydrating treatment.",
    duration: "60 min",
    price: 99
  },
  {
    id: "facial-2",
    name: "Collagen Induction",
    description: "Step into your glow. Glass skin unlocked.",
    duration: "60 min",
    price: 135
  },
  {
    id: "massage-1",
    name: "Lash Lift",
    description: "Enhance your natural lashes with a curled, wide-eyed look without extensions.",
    duration: "60 min",
    price: 65
  },
  {
    id: "massage-2",
    name: "Waxing Services",
    description: "Wax on, glow up - because smooth skin is a vibe.",
    duration: "10-55 min",
    price: "12-85"
  },
  {
    id: "package-1",
    name: "The Glow Edit",
    description: "Full-glam moment. It's the all-in-one, because you deserve the works.",
    duration: "90 min",
    price: 145
  },
  {
    id: "facial-3",
    name: "A La Carte",
    description: "Specialty add ons. Brow Tinting, Skin Lightening, Ear Seeding.",
    duration: "20 min",
    price: "17-53"
  }
];

// Add cache for date time slots
const timeSlotCache = new Map<string, string[]>();

export default function BookingPage() {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    service: "",
    date: "",
    time: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    notes: "",
    consentAgreed: false,
    signature: ""
  });

  // Change the initial state of serviceOptions to an empty array instead of defaultServices
  const [serviceOptions, setServiceOptions] = useState<Service[]>([]);
  const [isLoadingServices, setIsLoadingServices] = useState(true);

  // Fetch services from Square
  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await fetch('/api/square/services');
        const data = await response.json();

        if (data.status === 'success' && data.services.length > 0) {
          // Transform Square services to match our UI format
          const formattedServices = data.services.map((service: any): Service => ({
            id: service.id,
            name: service.name,
            description: service.description || 'No description available',
            duration: service.duration,
            price: service.price === 'Variable' ? 'Variable' : service.price
          }));
          setServiceOptions(formattedServices);
        } else {
          // Only use default services if API fails
          setServiceOptions(defaultServices);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
        // Use the default services only if there's an error
        setServiceOptions(defaultServices);
      } finally {
        setIsLoadingServices(false);
      }
    };

    fetchServices();
  }, []);

  // Add booking fee constant
  const BOOKING_FEE = 25;

  // Handle URL parameters
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const serviceId = params.get('service');
      const success = params.get('success');
      const reference = params.get('reference');

      // Check if this is a successful submission return
      if (success === 'true' && reference) {
        setBookingReference(reference);
        setIsSubmitted(true);
        return;
      }

      // Handle service selection from URL
      if (serviceId && serviceOptions.some(service => service.id === serviceId)) {
        setFormData(prev => ({ ...prev, service: serviceId }));
        // If a service is pre-selected, move to the next step
        setStep(2);
      }
    }
  }, [serviceOptions]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [bookingReference, setBookingReference] = useState("");

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    const newValue = type === 'checkbox' ? checked : value;
    setFormData(prev => ({ ...prev, [name]: newValue }));
  };

  // Handle service selection
  const handleServiceSelect = (serviceId: string) => {
    setFormData(prev => ({ ...prev, service: serviceId }));
  };

  // Handle time slot selection
  const handleTimeSelect = (time: string) => {
    setFormData(prev => ({ ...prev, time }));
  };

  // Navigate to next step
  const nextStep = () => {
    setStep(prev => prev + 1);
  };

  // Navigate to previous step
  const prevStep = () => {
    setStep(prev => prev - 1);
  };

  // Generate a random booking reference
  const generateBookingReference = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'GBB-';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data before submitting
      if (!formData.service || !formData.date || !formData.time ||
          !formData.firstName || !formData.lastName || !formData.email || !formData.phone) {
        throw new Error('Please fill out all required fields');
      }

      console.log('Submitting booking:', {
        serviceId: formData.service,
        serviceName: selectedService?.name,
        date: formData.date,
        time: formData.time
      });

      // First create the booking in Square
      const squareBookingResponse = await fetch('/api/square/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          serviceId: formData.service,
          date: formData.date,
          time: formData.time,
          notes: formData.notes
        })
      });

      const squareData = await squareBookingResponse.json();

      console.log('Square booking response:', squareData);

      if (squareData.status !== 'success') {
        throw new Error(squareData.message || 'Failed to create booking');
      }

      // Generate booking reference
      const reference = generateBookingReference();
      setBookingReference(reference);

      // Get selected service name
      const serviceName = selectedService?.name || formData.service;

      // Calculate total price and remaining balance
      const servicePrice = typeof selectedService?.price === 'string'
        ? selectedService?.price
        : Number(selectedService?.price);

      // Store booking in localStorage with fee information
      const booking = {
        ...formData,
        reference,
        createdAt: new Date().toISOString(),
        status: 'confirmed',
        bookingFee: BOOKING_FEE,
        servicePrice,
        totalPrice: typeof servicePrice === 'string' ? `${servicePrice} + $${BOOKING_FEE} booking fee` : Number(servicePrice),
        squareBookingId: squareData.booking.id
      };

      // Get existing bookings or initialize empty array
      const existingBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
      localStorage.setItem('bookings', JSON.stringify([...existingBookings, booking]));

      try {
        // Send confirmation email
        const emailResult = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            reference: reference,
            serviceName: serviceName,
            date: formData.date,
            time: formData.time,
            name: `${formData.firstName} ${formData.lastName}`,
            email: formData.email,
            phone: formData.phone,
            signature: formData.signature,
            signatureDate: new Date().toLocaleDateString(),
            consentAgreed: formData.consentAgreed,
            bookingFee: BOOKING_FEE,
            servicePrice: selectedService?.price,
            serviceDuration: selectedService?.duration,
            serviceDescription: selectedService?.description,
            squareBookingId: squareData.booking.id
          })
        });

        if (!emailResult.ok) {
          console.warn('Email confirmation failed, but booking was created');
        }
      } catch (emailError) {
        console.warn('Email confirmation failed, but booking was created', emailError);
      }

      // Set submitted state to show confirmation
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting booking:', error);
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to create booking. Please try again.';
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get selected service details
  const selectedService = serviceOptions.find(service => service.id === formData.service);

  // Add loading state for time slots
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);

  // Get time slots based on selected date and service
  const getTimeSlots = useCallback(async (date: string, serviceId: string) => {
    if (!date || !serviceId) return [];

    // Create a cache key from date and service
    const cacheKey = `${date}-${serviceId}`;

    // Check if we have cached time slots for this date/service combination
    if (timeSlotCache.has(cacheKey)) {
      console.log('Using cached time slots for:', cacheKey);
      return timeSlotCache.get(cacheKey) || [];
    }

    try {
      console.log('Fetching time slots for:', {
        serviceId,
        date
      });

      const response = await fetch('/api/square/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          serviceId,
          startDate: date
        })
      });

      const data = await response.json();

      console.log('Availability API response:', data);

      if (data.status === 'success' && Array.isArray(data.availableSlots)) {
        // Transform availability data into time slots
        const slots = data.availableSlots.map((slot: SquareAvailabilitySlot) => {
          if (!slot.startAt) return null;

          try {
            // Parse the date, ensuring timezone is properly handled
            // If no timezone is specified in startAt, it's interpreted as local time
            const slotDate = new Date(slot.startAt);

            // Log for debugging
            console.log('Processing slot:', {
              iso: slot.startAt,
              parsed: slotDate.toString(),
              localTime: slotDate.toLocaleTimeString('en-US')
            });

            // Format the time in a user-friendly format
            return slotDate.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: true
            });
          } catch (e) {
            console.error('Error formatting time slot:', e);
            return null;
          }
        }).filter(Boolean) as string[]; // Remove any null values

        console.log('Transformed time slots:', slots);

        // Cache the results
        timeSlotCache.set(cacheKey, slots);

        // Sort time slots chronologically to ensure proper display order
        const sortedSlots = [...slots].sort((a, b) => {
          // Parse times like "10:00 AM" or "2:30 PM"
          const parseTime = (timeStr: string) => {
            const [timePart, period] = timeStr.split(' ');
            const parts = timePart.split(':').map(Number);
            let hours = parts[0];
            const minutes = parts[1];

            // Convert to 24-hour format for comparison
            if (period === 'PM' && hours < 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes; // Return minutes since midnight
          };

          return parseTime(a) - parseTime(b);
        });

        console.log('Sorted time slots:', sortedSlots);
        return sortedSlots;
      }

      console.log('No available slots found in API response');
      return [];
    } catch (error) {
      console.error('Error fetching time slots:', error);
      return [];
    }
  }, []);

  // Update time slots when date changes
  useEffect(() => {
    const updateTimeSlots = async () => {
      if (formData.date && formData.service) {
        setIsLoadingTimeSlots(true);
        setAvailableTimeSlots([]); // Clear previous slots while loading

        console.log('Requesting time slots for:', {
          date: formData.date,
          service: formData.service,
          serviceName: selectedService?.name
        });

        const slots = await getTimeSlots(formData.date, formData.service);
        console.log('Received time slots:', slots);

        setAvailableTimeSlots(slots);
        setIsLoadingTimeSlots(false);

        // Auto-select the first time slot if available
        if (slots.length > 0 && !formData.time) {
          handleTimeSelect(slots[0]);
        }
      } else {
        setAvailableTimeSlots([]);
      }
    };

    updateTimeSlots();
  }, [formData.date, formData.service, getTimeSlots, selectedService?.name, formData.time]);

  return (
    <>
      <BeamsBackground />


      {/* Add sparkles effect */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <SparklesCore
          background="transparent"
          minSize={0.6}
          maxSize={1.4}
          particleDensity={60}
          className="w-full h-full"
          particleColor="#8b5d53"
        />
      </div>

      {/* Add floating gradient orbs for additional effect */}
      <div className="fixed top-1/4 left-10 w-64 h-64 rounded-full bg-[#b07c70]/15 blur-3xl animate-float pointer-events-none"></div>
      <div className="fixed bottom-1/4 right-10 w-80 h-80 rounded-full bg-[#8b5d53]/10 blur-3xl animate-float-delay pointer-events-none"></div>
      <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-[#d4b2a7]/10 blur-3xl animate-pulse-slow pointer-events-none"></div>

      <div className="container mx-auto px-4 py-8 pt-24 md:pt-36 md:py-16 min-h-screen">
        <motion.div
          className="max-w-5xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="text-center mb-8 md:mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-light tracking-wide mb-4 md:mb-6 text-[#5d3f39] uppercase font-serif">Book Your Appointment</h1>
            <p className="text-lg text-[#5d3f39]/80 max-w-3xl mx-auto px-0 md:px-4 leading-relaxed font-light">
              Ready to glow? Schedule your appointment with us and take the first step towards radiant skin.
            </p>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-4 md:mt-8"></div>
          </motion.div>

          {isSubmitted ? (
            <motion.div
              className="max-w-md mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="border-[#8b5d53]/20 shadow-lg bg-white/90 backdrop-blur-sm overflow-hidden">
                <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-3 px-4 md:py-6 md:px-6">
                  <CardTitle className="text-xl text-[#5d3f39] text-center font-light tracking-wide uppercase font-serif">
                    {step === 1 && "Select a Service"}
                    {step === 2 && "Choose Date & Time"}
                    {step === 3 && "Booking Agreement"}
                    {step === 4 && "Your Information"}
                    {step === 5 && "Sign Agreement"}
                    {step === 6 && "Review & Confirm"}
                  </CardTitle>
                  <div className="flex justify-center mt-3 md:mt-4">
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5, 6].map((stepNumber) => (
                        <React.Fragment key={stepNumber}>
                          <div
                            className={`w-6 h-6 md:w-7 md:h-7 rounded-full flex items-center justify-center ${
                              step >= stepNumber
                                ? 'bg-[#8b5d53] text-white'
                                : 'bg-stone-200 text-stone-500'
                            }`}
                          >
                            {stepNumber}
                          </div>
                          {stepNumber < 6 && (
                            <div
                              className={`w-4 md:w-6 h-1 ${
                                step > stepNumber ? 'bg-[#8b5d53]' : 'bg-stone-200'
                              }`}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-4 md:pt-6 px-4 md:px-6">
                  <div className="text-center">
                    <div className="w-14 h-14 md:w-16 md:h-16 bg-[#8b5d53]/20 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4">
                      <Check className="w-7 h-7 md:w-8 md:h-8 text-[#8b5d53]" />
                    </div>

                    <h2 className="text-xl md:text-2xl font-light tracking-wide text-[#5d3f39] mb-2 uppercase font-serif">Booking Confirmed!</h2>
                    <p className="text-sm md:text-lg text-[#5d3f39]/80 mb-4 md:mb-6 font-light">
                      Your appointment has been successfully booked. We look forward to seeing you!
                    </p>

                    <div className="bg-[#8b5d53]/5 p-4 rounded-lg mb-6 inline-block border border-[#8b5d53]/20">
                      <p className="text-sm text-stone-500">Your booking reference</p>
                      <p className="text-xl font-mono font-bold text-[#8b5d53]">{bookingReference}</p>
                    </div>

                    <div className="space-y-4 text-left bg-[#8b5d53]/5 p-4 rounded-lg mb-6 border border-[#8b5d53]/20">
                      <div>
                        <p className="text-sm text-stone-500">Service</p>
                        <p className="font-medium text-stone-800">{selectedService?.name}</p>
                      </div>

                      <div className="flex justify-between">
                        <div>
                          <p className="text-sm text-stone-500">Date</p>
                          <p className="font-medium text-stone-800">{formatDateUtil(formData.date)}</p>
                        </div>

                        <div>
                          <p className="text-sm text-stone-500">Time</p>
                          <p className="font-medium text-stone-800">{formData.time}</p>
                        </div>
                      </div>

                      <div className="border-t border-[#8b5d53]/20 pt-3 mt-2">
                        <div className="flex justify-between">
                          <p className="text-sm text-stone-500">Booking Fee Paid</p>
                          <p className="font-medium text-stone-800">${BOOKING_FEE}</p>
                        </div>
                        <div className="flex justify-between mt-1">
                          <p className="text-sm text-stone-500">Balance Due at Appointment</p>
                          <p className="font-medium text-stone-800">
                            {typeof selectedService?.price === 'string' && selectedService?.price.includes('-')
                              ? `(varies based on service)`
                              : `$${Number(selectedService?.price) - BOOKING_FEE}`}
                          </p>
                        </div>
                      </div>
                    </div>

                    <p className="text-sm text-stone-500 mb-6">
                      A confirmation email has been sent to your email address ({formData.email}). A copy of your signed consent form has also been <NAME_EMAIL>. Please arrive 10 minutes before your appointment time.
                    </p>
                  </div>
                </CardContent>

                <CardFooter className="flex justify-center">
                  <Button
                    onClick={() => window.location.href = '/home'}
                    className="bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white border border-[#8b5d53]/20 shadow-sm"
                  >
                    Return to Home
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="border-[#8b5d53]/20 shadow-md bg-white/80 backdrop-blur-sm overflow-hidden">
                <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-3 px-4 md:py-6 md:px-6">
                  <CardTitle className="text-xl text-[#5d3f39] text-center font-light tracking-wide uppercase font-serif">
                    {step === 1 && "Select a Service"}
                    {step === 2 && "Choose Date & Time"}
                    {step === 3 && "Booking Agreement"}
                    {step === 4 && "Your Information"}
                    {step === 5 && "Sign Agreement"}
                    {step === 6 && "Review & Confirm"}
                  </CardTitle>
                  <div className="flex justify-center mt-3 md:mt-4">
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5, 6].map((stepNumber) => (
                        <React.Fragment key={stepNumber}>
                          <div
                            className={`w-6 h-6 md:w-7 md:h-7 rounded-full flex items-center justify-center ${
                              step >= stepNumber
                                ? 'bg-[#8b5d53] text-white'
                                : 'bg-stone-200 text-stone-500'
                            }`}
                          >
                            {stepNumber}
                          </div>
                          {stepNumber < 6 && (
                            <div
                              className={`w-4 md:w-6 h-1 ${
                                step > stepNumber ? 'bg-[#8b5d53]' : 'bg-stone-200'
                              }`}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-4 md:pt-6 px-4 md:px-6">
                  <form onSubmit={handleSubmit}>
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={step}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      >
                        {step === 1 && (
                          <div className="space-y-6">
                            {isLoadingServices ? (
                              <div className="flex flex-col items-center justify-center py-8">
                                <Loader className="w-8 h-8 text-[#8b5d53] animate-spin mb-4" />
                                <p className="text-[#5d3f39]/80">Loading available services...</p>
                              </div>
                            ) : (
                              serviceOptions.map((service) => (
                                <div
                                  key={service.id}
                                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                    formData.service === service.id
                                      ? 'border-[#8b5d53] bg-[#8b5d53]/10'
                                      : 'border-stone-200 hover:border-[#8b5d53]/20'
                                  }`}
                                  onClick={() => handleServiceSelect(service.id)}
                                >
                                  <div className="flex justify-between items-center">
                                    <div>
                                      <h3 className="font-medium text-[#8b5d53]">{service.name}</h3>
                                      <p className="text-sm text-stone-600">{service.description}</p>
                                      <div className="flex items-center mt-2 text-sm text-stone-500">
                                        <Clock className="w-4 h-4 mr-1" />
                                        <span>{service.duration}</span>
                                      </div>
                                    </div>
                                    <div className="text-right">
                                      <div className="font-semibold text-[#8b5d53]">
                                        {typeof service.price === 'number'
                                          ? `$${service.price}`
                                          : service.price === 'Variable'
                                            ? 'Variable Price'
                                            : service.price}
                                      </div>
                                      {formData.service === service.id && (
                                        <div className="w-6 h-6 bg-[#8b5d53] rounded-full flex items-center justify-center mt-2">
                                          <Check className="w-4 h-4 text-white" />
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))
                            )}
                          </div>
                        )}

                        {step === 2 && (
                          <div className="space-y-6">
                            <div>
                              <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                Select Date
                              </label>
                              <div className="relative">
                                <input
                                  type="date"
                                  name="date"
                                  value={formData.date}
                                  onChange={handleChange}
                                  min={new Date().toISOString().split('T')[0]}
                                  className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                                  required
                                />
                                <div className="mt-2 text-xs text-stone-500">
                                  <p>Select a date to see available time slots. We&apos;re open Monday-Friday.</p>
                                  <button
                                    type="button"
                                    className="text-[#8b5d53] underline mt-1 font-medium"
                                    onClick={() => {
                                      const nextDate = getNextAvailableDate();
                                      setFormData(prev => ({ ...prev, date: nextDate.toISOString().split('T')[0] }));
                                    }}
                                  >
                                    Find next available date
                                  </button>
                                </div>
                              </div>
                            </div>

                            {formData.date && (
                              <div>
                                <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                  Select Time
                                </label>
                                <div className="grid grid-cols-3 gap-2">
                                  {isLoadingTimeSlots ? (
                                    <div className="col-span-3 p-4 text-center text-stone-500 bg-[#8b5d53]/5 rounded-md border border-[#8b5d53]/20">
                                      <div className="flex items-center justify-center">
                                        <Loader className="w-5 h-5 text-[#8b5d53] animate-spin mr-2" />
                                        <span>Loading available times...</span>
                                      </div>
                                    </div>
                                  ) : availableTimeSlots.length > 0 ? availableTimeSlots.map((time) => (
                                    <div
                                      key={time}
                                      className={`p-2 border rounded-md text-center cursor-pointer transition-all ${
                                        formData.time === time
                                          ? 'border-[#8b5d53] bg-[#8b5d53] text-white'
                                          : 'border-[#8b5d53]/20 hover:border-[#8b5d53]/50 text-stone-700'
                                      }`}
                                      onClick={() => handleTimeSelect(time)}
                                    >
                                      {time}
                                    </div>
                                  )) : (
                                    <div className="col-span-3 p-4 text-center text-stone-500 bg-[#8b5d53]/5 rounded-md border border-[#8b5d53]/20">
                                      No available time slots for this date. Please select another date.
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {step === 3 && (
                          <div className="space-y-6">
                            <div className="bg-[#8b5d53]/5 p-6 rounded-lg border border-[#8b5d53]/20">
                              <h3 className="text-xl font-medium text-[#5d3f39] mb-4 text-center">Booking & Service Agreement</h3>

                              <div className="space-y-4">
                                <div className="flex items-start gap-4 p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <div className="text-[#8b5d53] mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-info">
                                      <circle cx="12" cy="12" r="10" />
                                      <path d="M12 16v-4" />
                                      <path d="M12 8h.01" />
                                    </svg>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-[#5d3f39] mb-2">Secure Your Appointment</h4>
                                    <p className="text-[#5d3f39]/80 text-sm">
                                      To secure your appointment, a <span className="font-semibold">${BOOKING_FEE} booking fee</span> is required.
                                      This fee will be applied toward your service total on the day of your appointment.
                                    </p>
                                  </div>
                                </div>

                                <div className="flex items-start gap-4 p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <div className="text-[#8b5d53] mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-flask-conical">
                                      <path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2" />
                                      <path d="M8.5 2h7" />
                                      <path d="M7 16h10" />
                                    </svg>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-[#5d3f39] mb-2">Service Acknowledgment</h4>
                                    <p className="text-[#5d3f39]/80 text-sm">
                                      I understand that beauty services involve the use of professional-grade products and chemicals.
                                      I acknowledge that there are inherent risks, and results may vary. A detailed consent form will be
                                      provided before finalizing my booking.
                                    </p>
                                  </div>
                                </div>

                                <div className="flex items-start gap-4 p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <div className="text-[#8b5d53] mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar-clock">
                                      <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.5" />
                                      <path d="M16 2v4" />
                                      <path d="M8 2v4" />
                                      <path d="M3 10h18" />
                                      <circle cx="18" cy="18" r="4" />
                                      <path d="M18 16.5V18l1 1" />
                                    </svg>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-[#5d3f39] mb-2">Cancellation Policy</h4>
                                    <p className="text-[#5d3f39]/80 text-sm">
                                      If you need to cancel or reschedule, please provide at least 24 hours notice.
                                      Cancellations with less than 24 hours notice will result in forfeiture of the booking fee.
                                    </p>
                                  </div>
                                </div>

                                <div className="flex items-start gap-4 p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <div className="text-[#8b5d53] mt-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-shield-check">
                                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                                      <path d="m9 12 2 2 4-4" />
                                    </svg>
                                  </div>
                                  <div>
                                    <h4 className="font-medium text-[#5d3f39] mb-2">Liability Waiver</h4>
                                    <p className="text-[#5d3f39]/80 text-sm">
                                      By proceeding with this booking, I acknowledge that I will be required to sign a liability waiver
                                      before my appointment. This waiver releases GlowByBry from liability for any adverse reactions
                                      or unsatisfactory results from services provided.
                                    </p>
                                  </div>
                                </div>
                              </div>

                              <div className="mt-6 flex items-start gap-3">
                                <input
                                  type="checkbox"
                                  id="consentAgreed"
                                  name="consentAgreed"
                                  checked={formData.consentAgreed as boolean}
                                  onChange={handleChange}
                                  className="mt-1 h-4 w-4 rounded border-[#8b5d53]/30 text-[#8b5d53] focus:ring-[#8b5d53]/30"
                                  required
                                />
                                <label htmlFor="consentAgreed" className="text-[#5d3f39]/90 text-sm">
                                  I understand and agree to pay a ${BOOKING_FEE} booking fee to secure my appointment.
                                  I acknowledge the use of chemicals and products in beauty services and understand
                                  I will be required to sign a detailed liability waiver before my appointment is confirmed.
                                </label>
                              </div>
                            </div>
                          </div>
                        )}

                        {step === 4 && (
                          <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                  First Name
                                </label>
                                <input
                                  type="text"
                                  name="firstName"
                                  value={formData.firstName}
                                  onChange={handleChange}
                                  className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                                  required
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                  Last Name
                                </label>
                                <input
                                  type="text"
                                  name="lastName"
                                  value={formData.lastName}
                                  onChange={handleChange}
                                  className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                                  required
                                />
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                Email Address
                              </label>
                              <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                                required
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                Phone Number
                              </label>
                              <input
                                type="tel"
                                name="phone"
                                value={formData.phone}
                                onChange={handleChange}
                                className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                                required
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-[#8b5d53] mb-2">
                                Special Requests (Optional)
                              </label>
                              <textarea
                                name="notes"
                                value={formData.notes}
                                onChange={handleChange}
                                rows={3}
                                className="w-full p-2 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53]"
                              />
                            </div>
                          </div>
                        )}

                        {step === 5 && (
                          <div className="space-y-6">
                            <div className="bg-[#8b5d53]/5 p-6 rounded-lg border border-[#8b5d53]/20">
                              <h3 className="text-xl font-medium text-[#5d3f39] mb-4 text-center">Service Consent & Liability Waiver</h3>

                              <div className="space-y-5">
                                <div className="p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <h4 className="font-medium text-[#5d3f39] mb-3">Service Acknowledgment & Consent</h4>
                                  <div className="text-[#5d3f39]/80 text-sm space-y-3">
                                    <p>
                                      I, <span className="font-semibold">{formData.firstName} {formData.lastName}</span>,
                                      hereby consent to receive beauty services from GlowByBry. I acknowledge and agree to the following:
                                    </p>
                                    <ol className="list-decimal pl-5 space-y-2">
                                      <li>I understand that beauty services may involve the use of chemicals, products, tools, and equipment that could potentially cause adverse reactions.</li>
                                      <li>I acknowledge that GlowByBry uses professional-grade products and chemicals including but not limited to: cleansers, exfoliants, serums, masks, waxes, tints, adhesives, and other beauty treatment products.</li>
                                      <li>I confirm that I have disclosed all allergies, skin conditions, medications, and health concerns that could affect my treatment.</li>
                                      <li>I understand that results may vary and that there are no guarantees regarding the outcome of my treatment.</li>
                                      <li>I agree to follow all pre-care and post-care instructions provided by GlowByBry.</li>
                                      <li>I understand that I may experience temporary discomfort, redness, swelling, or irritation following certain treatments.</li>
                                    </ol>
                                  </div>
                                </div>

                                <div className="p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <h4 className="font-medium text-[#5d3f39] mb-3">Liability Waiver & Release</h4>
                                  <div className="text-[#5d3f39]/80 text-sm space-y-3">
                                    <p>
                                      By signing this agreement, I hereby release and hold harmless GlowByBry, its owner, employees, and representatives from any and all liability, claims, demands, actions, and causes of action whatsoever arising out of or related to any loss, damage, or injury that may be sustained by me while receiving services.
                                    </p>
                                    <p>
                                      I understand and acknowledge that:
                                    </p>
                                    <ol className="list-decimal pl-5 space-y-2">
                                      <li>I am voluntarily participating in these beauty services with full knowledge of the potential risks and benefits.</li>
                                      <li>I waive any right to bring legal action against GlowByBry for any injury, adverse reaction, or unsatisfactory results, unless caused by gross negligence.</li>
                                      <li>I agree to indemnify GlowByBry against any claims, legal actions, or damages brought by me or on my behalf related to services received.</li>
                                      <li>I understand that this waiver extends to any reaction, result, or unsatisfactory outcome that may occur as a result of the beauty services provided.</li>
                                    </ol>
                                  </div>
                                </div>

                                <div className="p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <h4 className="font-medium text-[#5d3f39] mb-3">Payment & Cancellation Policy</h4>
                                  <div className="text-[#5d3f39]/80 text-sm space-y-3">
                                    <ol className="list-decimal pl-5 space-y-2">
                                      <li>I agree to pay a non-refundable ${BOOKING_FEE} booking fee to secure my appointment.</li>
                                      <li>I understand this fee will be applied toward my service total on the day of my appointment.</li>
                                      <li>I acknowledge that cancellations with less than 24 hours notice will result in forfeiture of the booking fee.</li>
                                      <li>I agree to pay the remaining balance at the time of my appointment.</li>
                                      <li>I understand that GlowByBry reserves the right to refuse service to anyone.</li>
                                    </ol>
                                    <p className="mt-3 font-medium">
                                      This agreement constitutes a legally binding contract between myself and GlowByBry.
                                    </p>
                                  </div>
                                </div>

                                <div className="p-4 bg-white rounded-lg border border-[#8b5d53]/20">
                                  <h4 className="font-medium text-[#5d3f39] mb-3">Electronic Signature</h4>
                                  <p className="text-[#5d3f39]/80 text-sm mb-4">
                                    Please type your full name below to sign this agreement electronically.
                                    Your electronic signature is legally binding and equivalent to a handwritten signature.
                                  </p>

                                  <div className="space-y-3">
                                    <label className="block text-sm font-medium text-[#8b5d53]">
                                      Full Name Signature <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                      type="text"
                                      name="signature"
                                      value={formData.signature}
                                      onChange={handleChange}
                                      placeholder="Type your full name (e.g., Jane Marie Doe)"
                                      className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-2 focus:ring-[#8b5d53]/30 focus:border-[#8b5d53] font-medium"
                                      required
                                    />
                                    <p className="text-xs text-[#5d3f39]/60 italic">
                                      By typing my name above, I certify that I have read, understand, and agree to all terms outlined in this Service Consent & Liability Waiver.
                                      I acknowledge the potential risks associated with beauty services, including the use of chemicals and products.
                                      I voluntarily waive my right to bring legal action against GlowByBry for any adverse reactions or unsatisfactory results.
                                    </p>
                                    <p className="text-xs text-[#5d3f39]/60 italic">
                                      Date: {new Date().toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {step === 6 && (
                          <div className="space-y-6">
                            <div className="bg-[#8b5d53]/5 p-4 rounded-lg border border-[#8b5d53]/20">
                              <h3 className="font-medium text-[#8b5d53] mb-4">Booking Summary</h3>

                              <div className="space-y-3">
                                <div className="flex justify-between">
                                  <span className="text-stone-600">Service:</span>
                                  <span className="font-medium text-[#8b5d53]">{selectedService?.name}</span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-stone-600">Duration:</span>
                                  <span className="font-medium text-[#8b5d53]">{selectedService?.duration}</span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-stone-600">Date:</span>
                                  <span className="font-medium text-[#8b5d53]">{formatDateUtil(formData.date)}</span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-stone-600">Time:</span>
                                  <span className="font-medium text-[#8b5d53]">{formData.time}</span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-stone-600">Service Price:</span>
                                  <span className="font-medium text-[#8b5d53]">
                                    {typeof selectedService?.price === 'number'
                                      ? `$${selectedService?.price}`
                                      : selectedService?.price === 'Variable'
                                        ? 'Variable Price'
                                        : selectedService?.price}
                                  </span>
                                </div>

                                <div className="flex justify-between">
                                  <span className="text-stone-600">Booking Fee:</span>
                                  <span className="font-medium text-[#8b5d53]">${BOOKING_FEE}</span>
                                </div>

                                <div className="border-t border-[#8b5d53]/20 pt-2 mt-2">
                                  <div className="flex justify-between font-semibold">
                                    <span className="text-stone-600">Total Due Today:</span>
                                    <span className="text-[#8b5d53]">${BOOKING_FEE}</span>
                                  </div>
                                  <div className="flex justify-between text-sm mt-1">
                                    <span className="text-stone-500">Remaining balance due at appointment:</span>
                                    <span className="text-stone-500">
                                      {typeof selectedService?.price === 'number'
                                        ? `$${Number(selectedService?.price) - BOOKING_FEE}`
                                        : selectedService?.price === 'Variable'
                                          ? 'To be determined'
                                          : 'Variable based on service'}
                                    </span>
                                  </div>
                                </div>

                                <div className="border-t border-[#8b5d53]/20 my-2 pt-2">
                                  <div className="flex justify-between">
                                    <span className="text-stone-600">Name:</span>
                                    <span className="font-medium text-[#8b5d53]">{formData.firstName} {formData.lastName}</span>
                                  </div>

                                  <div className="flex justify-between">
                                    <span className="text-stone-600">Email:</span>
                                    <span className="font-medium text-[#8b5d53]">{formData.email}</span>
                                  </div>

                                  <div className="flex justify-between">
                                    <span className="text-stone-600">Phone:</span>
                                    <span className="font-medium text-[#8b5d53]">{formData.phone}</span>
                                  </div>
                                </div>

                                {formData.notes && (
                                  <div>
                                    <span className="text-stone-600 block">Special Requests:</span>
                                    <span className="font-medium text-[#8b5d53]">{formData.notes}</span>
                                  </div>
                                )}

                                <div className="border-t border-[#8b5d53]/20 my-2 pt-2">
                                  <div>
                                    <span className="text-stone-600 block">Digital Signature:</span>
                                    <span className="font-medium text-[#8b5d53] italic">{formData.signature}</span>
                                    <div className="text-xs text-stone-500 mt-1">Signed on: {new Date().toLocaleDateString()}</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="bg-[#8b5d53]/10 p-4 rounded-lg border border-[#8b5d53]/20">
                              <p className="text-[#8b5d53] text-sm">
                                By confirming your booking, you agree to our cancellation policy and to pay the $25 booking fee.
                                This fee secures your appointment and will be applied to your total. The remaining balance will be due at your appointment.
                                Please provide at least 24 hours notice for cancellations or rescheduling.
                              </p>
                            </div>
                          </div>
                        )}
                      </motion.div>
                    </AnimatePresence>
                  </form>
                </CardContent>

                <CardFooter className="flex justify-between py-3 px-4 md:py-4 md:px-6">
                  {step > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={prevStep}
                      className="flex items-center gap-1 md:gap-2 border-[#8b5d53]/20 text-[#8b5d53] hover:bg-[#8b5d53]/10 text-sm md:text-base px-2 md:px-4"
                    >
                      <ArrowLeft className="w-3 h-3 md:w-4 md:h-4" /> Back
                    </Button>
                  )}

                  {step < 6 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      disabled={
                        (step === 1 && !formData.service) ||
                        (step === 2 && (!formData.date || !formData.time)) ||
                        (step === 3 && !formData.consentAgreed) ||
                        (step === 4 && (!formData.firstName || !formData.lastName || !formData.email || !formData.phone)) ||
                        (step === 5 && (!formData.signature || formData.signature.trim().length < 3))
                      }
                      className="bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white border border-[#8b5d53]/20 shadow-sm ml-auto flex items-center gap-1 md:gap-2 text-sm md:text-base px-2 md:px-4"
                    >
                      Next <ArrowRight className="w-3 h-3 md:w-4 md:h-4" />
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white border border-[#8b5d53]/20 shadow-sm ml-auto text-sm md:text-base px-3 md:px-4"
                    >
                      {isSubmitting ? 'Processing...' : 'Confirm Booking'}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </motion.div>
          )}
        </motion.div>
      </div>
    </>
  );
}