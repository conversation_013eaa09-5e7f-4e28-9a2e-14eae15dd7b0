"use client";

import { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface AnimatedGradientBackgroundProps {
    className?: string;
    intensity?: "subtle" | "medium" | "strong";
}

interface Beam {
    x: number;
    y: number;
    width: number;
    length: number;
    angle: number;
    speed: number;
    opacity: number;
    color: string;
    pulse: number;
    pulseSpeed: number;
}

function createBeam(width: number, height: number): Beam {
    // More horizontal beams for an elegant look
    const angle = -10 + Math.random() * 20;
    // Updated refined luxury spa color palette centered around #8b5d53 and #d4b2a7
    const colors = [
        "#d4b2a7", // Dusty rose
        "#8b5d53", // Mocha
        "#e8d0c9", // Soft rose gold
        "#b07c70", // Medium rose
        "#f8f1ee", // Cream white
    ];

    return {
        x: Math.random() * width * 1.5 - width * 0.25,
        y: Math.random() * height * 1.5 - height * 0.25,
        width: 40 + Math.random() * 120, // Wider beams
        length: height * 1.5, // Shorter beams
        angle: angle,
        speed: 0.2 + Math.random() * 0.4, // Slower movement for elegance
        opacity: 0.04 + Math.random() * 0.06, // Much more subtle opacity
        color: colors[Math.floor(Math.random() * colors.length)],
        pulse: Math.random() * Math.PI * 2,
        pulseSpeed: 0.005 + Math.random() * 0.01, // Slower pulsing
    };
}

export function BeamsBackground({
    className,
    intensity = "medium",
}: AnimatedGradientBackgroundProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const beamsRef = useRef<Beam[] | null>(null);
    const animationFrameRef = useRef<number>(0);
    const [hasMounted, setHasMounted] = useState(false);
    const MINIMUM_BEAMS = 12; // Fewer beams for a cleaner look

    const opacityMap = {
        subtle: 0.4,
        medium: 0.5,
        strong: 0.6,
    };

    useEffect(() => {
        setHasMounted(true);
    }, []);

    useEffect(() => {
        if (!hasMounted) return;

        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        if (beamsRef.current === null) {
             const totalBeams = MINIMUM_BEAMS;
             const initialWidth = window.innerWidth;
             const initialHeight = window.innerHeight;
             beamsRef.current = Array.from({ length: totalBeams }, () =>
                 createBeam(initialWidth, initialHeight)
             );
        }

        const updateCanvasSize = () => {
            const dpr = window.devicePixelRatio || 1;
            canvas.width = window.innerWidth * dpr;
            canvas.height = window.innerHeight * dpr;
            canvas.style.width = `${window.innerWidth}px`;
            canvas.style.height = `${window.innerHeight}px`;
            ctx.scale(dpr, dpr);

            const totalBeams = MINIMUM_BEAMS;
            beamsRef.current = Array.from({ length: totalBeams }, () =>
                createBeam(canvas.width / dpr, canvas.height / dpr)
            );
        };

        updateCanvasSize();
        window.addEventListener("resize", updateCanvasSize);

        function resetBeam(beam: Beam, index: number, totalBeams: number) {
            if (!canvas) return beam;
            const dpr = window.devicePixelRatio || 1;
            const scaledWidth = canvas.width / dpr;
            const scaledHeight = canvas.height / dpr;
            const column = index % 4;
            const spacing = scaledWidth / 4;
            beam.y = scaledHeight + 100;
            beam.x = column * spacing + spacing / 2 + (Math.random() - 0.5) * spacing * 0.5;
            beam.width = 100 + Math.random() * 150;
            beam.speed = 0.1 + Math.random() * 0.2;

            // Updated refined luxury spa color palette
            const colors = [
                "#d4b2a7", // Dusty rose
                "#8b5d53", // Mocha
                "#e8d0c9", // Soft rose gold
                "#b07c70", // Medium rose
                "#f8f1ee", // Cream white
            ];
            // Use totalBeams to create more variation in color distribution
            beam.color = colors[(index + Math.floor(totalBeams/4)) % colors.length];

            beam.opacity = 0.03 + Math.random() * 0.05;
            beam.length = scaledHeight * 1.5;
            return beam;
        }

        function drawBeam(ctx: CanvasRenderingContext2D, beam: Beam) {
            if (!beamsRef.current) return;
            ctx.save();
            ctx.translate(beam.x, beam.y);
            ctx.rotate((beam.angle * Math.PI) / 180);
            const pulsingOpacity = beam.opacity * (0.8 + Math.sin(beam.pulse) * 0.2) * opacityMap[intensity];

            const gradient = ctx.createLinearGradient(0, 0, 0, beam.length);
            // Parse color for gradient creation
            const baseColor = beam.color;

            gradient.addColorStop(0, `${baseColor}00`); // Fully transparent
            gradient.addColorStop(0.1, `${baseColor}${Math.round(pulsingOpacity * 30).toString(16).padStart(2, '0')}`);
            gradient.addColorStop(0.4, `${baseColor}${Math.round(pulsingOpacity * 255).toString(16).padStart(2, '0')}`);
            gradient.addColorStop(0.6, `${baseColor}${Math.round(pulsingOpacity * 255).toString(16).padStart(2, '0')}`);
            gradient.addColorStop(0.9, `${baseColor}${Math.round(pulsingOpacity * 30).toString(16).padStart(2, '0')}`);
            gradient.addColorStop(1, `${baseColor}00`); // Fully transparent

            ctx.fillStyle = gradient;
            ctx.fillRect(-beam.width / 2, 0, beam.width, beam.length);
            ctx.restore();
        }

        function animate() {
            if (!canvas || !ctx || !beamsRef.current) return;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const totalBeams = beamsRef.current.length;
            beamsRef.current.forEach((beam, index) => {
                beam.y -= beam.speed;
                beam.pulse += beam.pulseSpeed;
                if (beam.y + beam.length < -100) {
                    resetBeam(beam, index, totalBeams);
                }
                drawBeam(ctx, beam);
            });
            animationFrameRef.current = requestAnimationFrame(animate);
        }

        animate();

        return () => {
            window.removeEventListener("resize", updateCanvasSize);
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
            }
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [intensity, hasMounted]);

    return (
        <div
            className={cn(
                "fixed inset-0 -z-10 w-full overflow-hidden bg-gradient-to-b from-[#faf7f6] via-[#f5ede9] to-[#f0e4df]",
                className
            )}
        >
            {hasMounted && (
                 <canvas
                    ref={canvasRef}
                    className="absolute inset-0"
                    style={{ filter: "blur(15px)" }}
                />
            )}
            <motion.div
                className="absolute inset-0 bg-[#8b5d53]/5"
                animate={{ opacity: [0.02, 0.05, 0.02] }}
                transition={{ duration: 8, ease: "easeInOut", repeat: Number.POSITIVE_INFINITY }}
            />
        </div>
    );
}