export interface NavItem {
  name: string;
  url: string;
  icon: string; // Use string identifier instead of component
}

// Shared navigation items to prevent re-mounting of NavBar component
export const navigationItems: NavItem[] = [
  { name: 'Home', url: '/home', icon: 'Home' },
  { name: 'Services', url: '/services', icon: 'List' },
  { name: 'Booking', url: '/booking', icon: 'Clock' },
  { name: 'Contact', url: '/contact', icon: 'Phone' }
];
