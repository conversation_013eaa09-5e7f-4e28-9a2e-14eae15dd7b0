"use client"

import React, { useRef, create<PERSON>ontext, useContext, useMemo } from "react"
import { motion, useMotionValue, useTransform, useSpring, MotionValue, PanInfo } from "framer-motion"
import { cn } from "@/lib/utils"

// Context to share motion values
interface FloatingContextType {
  mouseX: MotionValue<number>
  mouseY: MotionValue<number>
  sensitivity: number
}

const FloatingContext = createContext<FloatingContextType | undefined>(undefined)

// Custom hook to access the context
const useFloatingContext = () => {
  const context = useContext(FloatingContext)
  if (!context) {
    throw new Error("useFloatingContext must be used within a Floating provider")
  }
  return context
}

// Use ComponentProps<typeof motion.div> to correctly inherit motion props
type MotionDivProps = React.ComponentProps<typeof motion.div>

// Floating Container Component
interface FloatingProps extends Omit<MotionDivProps, 'onDrag'> { // Omit conflicting onDrag if needed, or handle specifically
  sensitivity?: number
  // Explicitly define onDrag if you intend to use it with framer-motion's signature
  onDrag?: (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void;
}

const Floating: React.FC<FloatingProps> = ({
  children,
  className,
  sensitivity = 1, // Default sensitivity
  onMouseMove,
  onMouseLeave,
  ...props // Spread remaining motion props
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  // Explicitly type the event handler passed to the DOM element
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (onMouseMove) {
        // If a custom onMouseMove is passed, call it
        // Note: The signature might differ from the DOM event. Adjust if needed.
        // onMouseMove(event as any); // Example, adjust type casting if necessary
    }
    if (!containerRef.current) return
    const { left, top, width, height } = containerRef.current.getBoundingClientRect()
    const x = (event.clientX - left) / width - 0.5 // Normalize to -0.5 to 0.5
    const y = (event.clientY - top) / height - 0.5 // Normalize to -0.5 to 0.5
    mouseX.set(x)
    mouseY.set(y)
  }

  const handleMouseLeave = () => {
     if (onMouseLeave) {
        // If a custom onMouseLeave is passed, call it
    }
    mouseX.set(0)
    mouseY.set(0)
  }

  // Spring animations for smoother transitions
  const springConfig = { damping: 20, stiffness: 150, mass: 0.5 }
  const springX = useSpring(mouseX, springConfig)
  const springY = useSpring(mouseY, springConfig)

  const contextValue = useMemo(() => ({
    mouseX: springX,
    mouseY: springY,
    sensitivity,
  }), [springX, springY, sensitivity])

  return (
    <FloatingContext.Provider value={contextValue}>
      {/* Pass event handlers directly to motion.div */}
      <motion.div
        ref={containerRef}
        className={cn("relative w-full h-full", className)}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        {...props} // Pass the rest of the motion props
      >
        {children}
      </motion.div>
    </FloatingContext.Provider>
  )
}

// Floating Element Component
interface FloatingElementProps extends Omit<MotionDivProps, 'onDrag'> { // Omit conflicting onDrag
  depth?: number
  onDrag?: (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void;
}

const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  className,
  depth = 1, // Default depth multiplier
  style,
  ...props // Spread remaining motion props
}) => {
  const { mouseX, mouseY, sensitivity } = useFloatingContext()

  // Adjust movement based on depth and sensitivity
  // Negative sensitivity reverses the direction
  const moveX = useTransform(mouseX, (latest) => latest * depth * sensitivity * 20) // Multiplied for visual effect
  const moveY = useTransform(mouseY, (latest) => latest * depth * sensitivity * 20)

  return (
    <motion.div
      className={cn("absolute", className)}
      style={{
        ...style,
        translateX: moveX,
        translateY: moveY,
        willChange: "transform", // Optimize performance
      }}
      {...props} // Pass the rest of the motion props
    >
      {children}
    </motion.div>
  )
}

Floating.displayName = "Floating"
FloatingElement.displayName = "FloatingElement"

export { Floating, FloatingElement }
export type { FloatingProps, FloatingElementProps }