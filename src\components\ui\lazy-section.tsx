"use client";

import { useEffect, useRef, useState } from "react";
import { motion, Variants } from "framer-motion";

interface LazyLoadSectionProps {
  children: React.ReactNode;
  threshold?: number;
  className?: string;
  animationVariants?: Variants;
}

/**
 * LazyLoadSection component that only renders its children when it comes into view
 * This helps improve performance by deferring the rendering of off-screen content
 */
export function LazyLoadSection({
  children,
  threshold = 0.1,
  className = "",
  animationVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  } as Variants,
}: LazyLoadSectionProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    observer.observe(ref.current);

    return () => {
      observer.disconnect();
    };
  }, [threshold]);

  return (
    <div ref={ref} className={className}>
      {isVisible ? (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={animationVariants}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          {children}
        </motion.div>
      ) : (
        <div className="min-h-[100px]" /> // Placeholder to prevent layout shifts
      )}
    </div>
  );
}
