import { testimonials as localTestimonials } from "@/data/testimonials";
import { reviewsConfig, filterReviewsByKeywords } from "@/config/reviews";

// Google Reviews API integration
// This can be configured to use Google Places API or third-party services

export interface GoogleReview {
  id: string;
  name: string;
  role: string;
  content: string;
  image: string;
  rating: number;
  date?: string;
  source?: 'google' | 'local';
}

// Configuration for Google Places API
interface GooglePlacesConfig {
  apiKey?: string;
  placeId?: string;
  businessName?: string;
}

// Function to fetch Google reviews using Google Places API
export async function fetchGoogleReviews(config?: GooglePlacesConfig): Promise<GoogleReview[]> {
  try {
    // If we have a Google Places API key and place ID, use the real API
    if (config?.apiKey && config?.placeId) {
      return await fetchFromGooglePlacesAPI(config.apiKey, config.placeId);
    }
    
    // Otherwise, call our internal API endpoint
    const response = await fetch('/api/reviews');
    
    if (!response.ok) {
      throw new Error('Failed to fetch reviews');
    }
    
    const data = await response.json();
    const reviews = data.success ? data.reviews : localTestimonials;
    
    // Return all reviews since these are Bryanna's reviews from Beauty Health and Wellness
    return reviews;
  } catch (error) {
    console.error("Error fetching Google reviews:", error);
    // Return local testimonials as fallback
    return localTestimonials;
  }
}

// Function to fetch reviews from Google Places API directly
async function fetchFromGooglePlacesAPI(apiKey: string, placeId: string): Promise<GoogleReview[]> {
  try {
    // Note: Due to CORS restrictions, we need to make this call through our API endpoint
    // Direct browser calls to Google Places API are not allowed
    const response = await fetch('/api/reviews', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        config: { apiKey, placeId },
        useRealAPI: true 
      }),
    });
    
    if (!response.ok) {
      throw new Error('Google Places API request failed');
    }
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(`Google Places API error: ${data.error}`);
    }
    
    // Return all reviews since these are Bryanna's reviews from Beauty Health and Wellness
    return data.reviews;
  } catch (error) {
    console.error('Error fetching from Google Places API:', error);
    throw error;
  }
}

// Function to fetch reviews from specific URLs or place IDs
export async function fetchReviewsFromUrls(urls: string[], config?: GooglePlacesConfig): Promise<GoogleReview[]> {
  try {
    // If URLs are provided, prioritize processing them to find businesses at those locations
    if (urls.length > 0) {
      console.log('URLs provided, processing them to find businesses...');
      
      // Pass URLs to our API endpoint for processing
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          urls, 
          config: config || {
            apiKey: reviewsConfig.google.apiKey,
            placeId: reviewsConfig.google.placeId
          },
          useRealAPI: reviewsConfig.api.forceRealAPI || !!(config?.apiKey && config?.placeId)
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews from URLs');
      }
      
      const data = await response.json();
      const reviews = data.success ? data.reviews : localTestimonials;
      
      // Return all reviews since these are Bryanna's reviews from Beauty Health and Wellness
      return reviews;
    }
    
    // If no URLs provided, fall back to configured Place ID
    if (config?.apiKey && config?.placeId) {
      console.log('No URLs provided, using configured Place ID');
      return await fetchFromGooglePlacesAPI(config.apiKey, config.placeId);
    }
    
    // Check if we have configuration from reviewsConfig
    if (reviewsConfig.google.apiKey && reviewsConfig.google.placeId && reviewsConfig.api.forceRealAPI) {
      console.log('Using real Google Places API from reviewsConfig');
      return await fetchFromGooglePlacesAPI(reviewsConfig.google.apiKey, reviewsConfig.google.placeId);
    }
    
    // Final fallback to local testimonials
    console.log('No configuration available, using local testimonials');
    return localTestimonials;
  } catch (error) {
    console.error("Error fetching reviews from URLs:", error);
    // Return local testimonials as fallback
    return localTestimonials;
  }
}

// Function to extract place ID from Google Maps URL
export function extractPlaceIdFromUrl(url: string): string | null {
  try {
    // Handle different Google Maps URL formats
    const patterns = [
      /place_id=([a-zA-Z0-9_-]+)/,
      /data=.*!1s([a-zA-Z0-9_-]+)/,
      /maps\/place\/[^/]+\/.*@.*\/data=.*!4m.*!3m.*!1s([a-zA-Z0-9_-]+)/,
      // New pattern for the review URLs you provided
      /1s([a-zA-Z0-9_-]+)!/,
      // Pattern for hex place IDs
      /0x([a-fA-F0-9]+):0x([a-fA-F0-9]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    
    // Try to extract coordinates and convert to place ID
    const coordMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (coordMatch) {
      const lat = coordMatch[1];
      const lng = coordMatch[2];
      console.log(`Found coordinates: ${lat}, ${lng}`);
      // We'll use these coordinates to find the place
      return `coords:${lat},${lng}`;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting place ID from URL:', error);
    return null;
  }
}

// New function to extract review data from Google Maps URLs
export async function extractReviewsFromGoogleMapsUrls(urls: string[]): Promise<GoogleReview[]> {
  const extractedReviews: GoogleReview[] = [];
  
  for (const url of urls) {
    try {
      console.log('Processing Google Maps URL:', url);
      
      // Extract place information from URL
      const placeInfo = extractPlaceInfoFromUrl(url);
      if (placeInfo) {
        console.log('Extracted place info:', placeInfo);
        
        // Try to fetch reviews using the extracted information
        const reviews = await fetchReviewsFromPlaceInfo(placeInfo);
        extractedReviews.push(...reviews);
      }
    } catch (error) {
      console.error('Error processing URL:', url, error);
    }
  }
  
  return extractedReviews;
}

// Helper function to extract place information from Google Maps URLs
function extractPlaceInfoFromUrl(url: string): any {
  try {
    // Extract coordinates
    const coordMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
    
    // Extract place reference
    const placeRefMatch = url.match(/1s([a-zA-Z0-9_-]+)!/);
    
    // Extract hex place ID
    const hexPlaceMatch = url.match(/0x([a-fA-F0-9]+):0x([a-fA-F0-9]+)/);
    
    return {
      coordinates: coordMatch ? { lat: coordMatch[1], lng: coordMatch[2] } : null,
      placeReference: placeRefMatch ? placeRefMatch[1] : null,
      hexPlaceId: hexPlaceMatch ? `0x${hexPlaceMatch[1]}:0x${hexPlaceMatch[2]}` : null,
      originalUrl: url
    };
  } catch (error) {
    console.error('Error extracting place info:', error);
    return null;
  }
}

// Helper function to fetch reviews from extracted place information
async function fetchReviewsFromPlaceInfo(placeInfo: any): Promise<GoogleReview[]> {
  try {
    // Try different approaches to get the place ID
    let placeId = null;
    
    // Method 1: Use coordinates to find place
    if (placeInfo.coordinates) {
      placeId = await findPlaceIdByCoordinates(placeInfo.coordinates.lat, placeInfo.coordinates.lng);
    }
    
    // Method 2: Try to convert hex place ID
    if (!placeId && placeInfo.hexPlaceId) {
      placeId = await convertHexPlaceId(placeInfo.hexPlaceId);
    }
    
    if (placeId) {
      console.log('Found place ID:', placeId);
      // Use our existing API to fetch reviews
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config: {
            apiKey: reviewsConfig.google.apiKey,
            placeId: placeId
          },
          useRealAPI: true
        })
      });
      
      const data = await response.json();
      return data.success ? data.reviews : [];
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching reviews from place info:', error);
    return [];
  }
}

// Helper function to find place ID using coordinates
async function findPlaceIdByCoordinates(lat: string, lng: string): Promise<string | null> {
  try {
    if (!reviewsConfig.google.apiKey) return null;
    
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=50&key=${reviewsConfig.google.apiKey}`
    );
    
    const data = await response.json();
    if (data.results && data.results.length > 0) {
      return data.results[0].place_id;
    }
    
    return null;
  } catch (error) {
    console.error('Error finding place by coordinates:', error);
    return null;
  }
}

// Helper function to convert hex place ID (if possible)
async function convertHexPlaceId(hexId: string): Promise<string | null> {
  // This is a placeholder - hex place IDs are internal Google identifiers
  // and can't be directly converted to public place IDs
  console.log('Hex place ID conversion not available:', hexId);
  return null;
}

// Helper function to get Google Business Profile URL
export function getGoogleBusinessUrl(businessName: string, location?: string): string {
  const query = location ? `${businessName} ${location}` : businessName;
  return `https://www.google.com/maps/search/${encodeURIComponent(query)}`;
} 