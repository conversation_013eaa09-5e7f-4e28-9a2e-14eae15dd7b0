"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import Image from "next/image";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Search,
  X
} from "lucide-react";

import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { AppointmentItem } from "@/components/admin/appointment-item";

const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

interface AppointmentData {
  id: string;
  clientName: string;
  clientImage: string;
  service: string;
  date: string;
  time: string;
  duration: string;
  status: "confirmed" | "completed" | "cancelled" | "no-show";
  price: string;
}

export default function AppointmentsPage() {
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"agenda" | "calendar">("agenda");
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Calculate the calendar days for the current month view
  const calendarDays = React.useMemo(() => {
    const days = [];
    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    // const _lastDay = new Date(year, month + 1, 0);

    // Get the first day to display (might be from the previous month)
    const firstDayOfWeek = firstDay.getDay();
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDayOfWeek);

    // Generate 42 days (6 weeks)
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      days.push({
        date: currentDate,
        isCurrentMonth: currentDate.getMonth() === month,
        isToday:
          currentDate.getDate() === new Date().getDate() &&
          currentDate.getMonth() === new Date().getMonth() &&
          currentDate.getFullYear() === new Date().getFullYear(),
      });
    }

    return days;
  }, [selectedDate]);

  // Sample data for appointments
  const appointments: AppointmentData[] = [
    {
      id: "a1",
      clientName: "Sarah Johnson",
      clientImage: "https://randomuser.me/api/portraits/women/11.jpg",
      service: "Hydrating Facial",
      date: "2023-08-15",
      time: "10:00 AM",
      duration: "60 min",
      status: "completed",
      price: "$89",
    },
    {
      id: "a2",
      clientName: "Michael Chen",
      clientImage: "https://randomuser.me/api/portraits/men/22.jpg",
      service: "Deep Tissue Massage",
      date: "2023-08-15",
      time: "2:30 PM",
      duration: "75 min",
      status: "completed",
      price: "$99",
    },
    {
      id: "a3",
      clientName: "Emily Rodriguez",
      clientImage: "https://randomuser.me/api/portraits/women/33.jpg",
      service: "Ultimate Glow Package",
      date: "2023-08-16",
      time: "11:00 AM",
      duration: "120 min",
      status: "confirmed",
      price: "$179",
    },
    {
      id: "a4",
      clientName: "David Williams",
      clientImage: "https://randomuser.me/api/portraits/men/44.jpg",
      service: "Anti-Aging Facial",
      date: "2023-08-16",
      time: "3:00 PM",
      duration: "60 min",
      status: "confirmed",
      price: "$109",
    },
    {
      id: "a5",
      clientName: "Sophia Martinez",
      clientImage: "https://randomuser.me/api/portraits/women/55.jpg",
      service: "Hot Stone Massage",
      date: "2023-08-17",
      time: "1:00 PM",
      duration: "90 min",
      status: "cancelled",
      price: "$119",
    },
    {
      id: "a6",
      clientName: "James Taylor",
      clientImage: "https://randomuser.me/api/portraits/men/66.jpg",
      service: "Express Facial",
      date: "2023-08-18",
      time: "9:30 AM",
      duration: "30 min",
      status: "no-show",
      price: "$59",
    },
  ];

  // Filter appointments based on search query and status filter
  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = searchQuery === "" ||
      appointment.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      appointment.service.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = filterStatus === null || appointment.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  const clearFilters = () => {
    setFilterStatus(null);
    setSearchQuery("");
  };

  const previousMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setSelectedDate(newDate);
  };

  const nextMonth = () => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setSelectedDate(newDate);
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="flex min-h-screen bg-stone-50">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader
          title="Appointments"
          onMenuClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <main className="flex-1 overflow-y-auto p-6">
          {/* Page header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-stone-800">Appointments</h1>
              <p className="text-sm text-stone-500 mt-1">Manage and schedule client appointments</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button className="bg-amber-500 hover:bg-amber-600">
                <Plus className="mr-2 h-4 w-4" /> New Appointment
              </Button>
            </div>
          </div>

          {/* Filters and search */}
          <div className="mb-6 bg-white p-4 rounded-lg shadow-sm border border-stone-200">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-stone-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by client or service..."
                  className="w-full pl-10 pr-4 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-stone-400 hover:text-stone-600"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>

              <div className="flex items-center gap-2">
                <select
                  className="px-4 py-2 bg-white border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  value={filterStatus || ""}
                  onChange={(e) => setFilterStatus(e.target.value || null)}
                >
                  <option value="">All Status</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="no-show">No Show</option>
                </select>

                <div className="flex items-center gap-2">
                  <button
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      viewMode === "agenda"
                        ? "bg-amber-100 text-amber-700"
                        : "text-stone-700 hover:bg-stone-100"
                    }`}
                    onClick={() => setViewMode("agenda")}
                  >
                    Agenda
                  </button>
                  <button
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      viewMode === "calendar"
                        ? "bg-amber-100 text-amber-700"
                        : "text-stone-700 hover:bg-stone-100"
                    }`}
                    onClick={() => setViewMode("calendar")}
                  >
                    Calendar
                  </button>
                </div>

                {(filterStatus || searchQuery) && (
                  <button
                    className="flex items-center gap-1 px-3 py-2 text-sm text-stone-600 hover:text-amber-600 transition-colors"
                    onClick={clearFilters}
                  >
                    <X className="h-3.5 w-3.5" /> Clear
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Appointments list or calendar view */}
          {viewMode === "agenda" ? (
            <div className="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden">
              <div className="p-6 border-b border-stone-200">
                <h2 className="text-lg font-semibold text-stone-800">Upcoming Appointments</h2>
              </div>

              {filteredAppointments.length > 0 ? (
                <div className="divide-y divide-stone-200">
                  {filteredAppointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="p-4 hover:bg-stone-50 transition-colors"
                    >
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <Image
                            width={40}
                            height={40}
                            className="rounded-full object-cover"
                            src={appointment.clientImage}
                            alt={appointment.clientName}
                          />
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div>
                              <div className="text-sm font-medium text-stone-900">{appointment.clientName}</div>
                              <div className="text-sm text-stone-500">{appointment.service} ({appointment.duration})</div>
                            </div>
                            <div className="flex items-center gap-3">
                              <div className="text-right">
                                <div className="text-sm font-medium text-stone-900">{appointment.date}</div>
                                <div className="text-sm text-stone-500">{appointment.time}</div>
                              </div>
                              <Badge
                                className={`
                                  ${appointment.status === 'confirmed' ? 'bg-blue-100 text-blue-800' : ''}
                                  ${appointment.status === 'completed' ? 'bg-emerald-100 text-emerald-800' : ''}
                                  ${appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' : ''}
                                  ${appointment.status === 'no-show' ? 'bg-stone-100 text-stone-800' : ''}
                                `}
                              >
                                {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                              </Badge>
                              <div className="text-sm font-semibold text-stone-900">{appointment.price}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center text-stone-500">
                  <Calendar className="mx-auto h-12 w-12 text-stone-300 mb-3" />
                  <h3 className="text-lg font-medium text-stone-800 mb-1">No appointments found</h3>
                  <p className="mb-4">Try adjusting your search or filters</p>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={clearFilters}
                  >
                    Reset Filters
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden">
              <div className="p-6 border-b border-stone-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-stone-800">Calendar View</h2>
                  <div className="flex items-center gap-2">
                    <button
                      className="p-1 rounded-md text-stone-500 hover:bg-stone-100 hover:text-stone-700"
                      onClick={previousMonth}
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    <span className="text-sm font-medium">
                      {selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                    </span>
                    <button
                      className="p-1 rounded-md text-stone-500 hover:bg-stone-100 hover:text-stone-700"
                      onClick={nextMonth}
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-4">
                <div className="grid grid-cols-7 gap-2 mb-2">
                  {daysOfWeek.map((day) => (
                    <div key={day} className="text-xs font-medium text-stone-500 text-center py-2">
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {calendarDays.map((day, index) => (
                    <div
                      key={index}
                      className={`
                        min-h-[80px] p-1 border rounded-md text-sm
                        ${day.isCurrentMonth ? 'bg-white' : 'bg-stone-50 text-stone-400'}
                        ${day.isToday ? 'border-amber-500' : 'border-stone-200'}
                      `}
                    >
                      <div className="text-right mb-1">
                        <span className={`
                          inline-flex h-6 w-6 items-center justify-center rounded-full text-xs
                          ${day.isToday ? 'bg-amber-500 text-white' : ''}
                        `}>
                          {day.date.getDate()}
                        </span>
                      </div>

                      {/* We would normally filter appointments for this day - simple example */}
                      {day.isCurrentMonth && day.date.getDate() === 16 && (
                        <div className="text-xs p-1 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                          11:00 AM - Emily
                        </div>
                      )}
                      {day.isCurrentMonth && day.date.getDate() === 16 && (
                        <div className="text-xs p-1 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                          3:00 PM - David
                        </div>
                      )}
                      {day.isCurrentMonth && day.date.getDate() === 17 && (
                        <div className="text-xs p-1 bg-red-100 text-red-800 rounded mb-1 truncate line-through">
                          1:00 PM - Sophia
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}