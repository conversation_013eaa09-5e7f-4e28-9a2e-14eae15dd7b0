import { NextResponse } from 'next/server';
import { bookingsApi, catalogApi } from './config';

export async function GET() {
  try {
    // Test the Square API connection
    const { result } = await catalogApi.listCatalog(undefined, 'ITEM');
    
    return NextResponse.json({ 
      status: 'success',
      message: 'Square API connected successfully',
      services: result.objects 
    });
  } catch (error) {
    console.error('Square API Error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Failed to connect to Square API',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // Handle different types of booking requests
    switch (body.action) {
      case 'searchAvailability':
        const availability = await bookingsApi.searchAvailability({
          query: {
            filter: {
              startAtRange: {
                startAt: body.startAt,
                endAt: body.endAt
              },
              locationId: process.env.SQUARE_LOCATION_ID
            }
          }
        });
        return NextResponse.json(availability.result);
        
      case 'createBooking':
        const booking = await bookingsApi.createBooking({
          booking: {
            startAt: body.startAt,
            locationId: process.env.SQUARE_LOCATION_ID,
            customerId: body.customerId,
            customerNote: body.notes,
            appointmentSegments: [{
              durationMinutes: body.duration,
              serviceVariationId: body.serviceId,
              teamMemberId: body.teamMemberId
            }]
          }
        });
        return NextResponse.json(booking.result);
        
      default:
        return NextResponse.json(
          { status: 'error', message: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Square API Error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Failed to process Square API request',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 