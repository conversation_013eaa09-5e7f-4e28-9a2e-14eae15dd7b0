"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { InstagramIcon, FacebookIcon, Mail, MapPin, Phone } from "lucide-react";

import { cn } from "@/lib/utils";

interface SiteFooterProps {
  className?: string;
}

export function SiteFooter({ className }: SiteFooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn("bg-gradient-to-b from-[#f0e4df] to-[#e8d0c9] text-[#8b5d53] py-8 border-t border-[#8b5d53]/10", className)}>
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10">
          {/* Column 1: About & Contact */}
          <div className="space-y-4 -mt-4">
            <div>
              <Image
                src="/glow-by-bry-logo.svg"
                alt="GlowByBry Logo"
                width={160}
                height={50}
                className="object-contain mb-6 -mt-4"
              />
              <h3 className="text-lg font-serif font-light text-[#8b5d53]/90 mb-4 -mt-3">Contact Us</h3>
              <div className="space-y-3 -mt-2">
                <div className="flex items-center">
                  <Phone className="w-4 h-4 mr-2.5 text-[#8b5d53] flex-shrink-0" />
                  <span className="text-stone-600 font-light font-serif">(*************</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-2.5 text-[#8b5d53] flex-shrink-0" />
                  <span className="text-stone-600 font-light font-serif"><EMAIL></span>
                </div>
                <div className="flex items-start">
                  <MapPin className="w-4 h-4 mr-2.5 mt-0.5 text-[#8b5d53] flex-shrink-0" />
                  <span className="text-stone-600 font-light font-serif">77935 Calle Tampico #103, La Quinta, CA 92253</span>
                </div>
              </div>
            </div>
          </div>

          {/* Column 2: Find Us Map */}
          <div className="space-y-4 ml-2">
            <h3 className="text-lg font-serif font-light text-[#8b5d53]/90 mb-4">Find Us</h3>
            <div className="space-y-3">
              <div className="w-full h-52 rounded-lg overflow-hidden border-2 border-[#8b5d53]/20 shadow-sm">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3315.012283492089!2d-116.30046402457166!3d33.68047193759761!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80daf43543d39c67%3A0x6b0bbf51f87edcc4!2s77935%20Calle%20Tampico%20%23103%2C%20La%20Quinta%2C%20CA%2092253!5e1!3m2!1sen!2sus!4v1720225001639!5m2!1sen!2sus"
                  className="w-full h-full border-0"
                  style={{ border: 0 }}
                  allowFullScreen={false}
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="GlowByBry Location"
                />
              </div>
              <p className="text-sm text-center text-stone-600">
                <Link
                  href="https://www.google.com/maps/place/77935+Calle+Tampico+%23103,+La+Quinta,+CA+92253"
                  target="_blank"
                  className="hover:text-[#8b5d53] transition-colors font-light font-serif"
                >
                  Open in Google Maps
                </Link>
              </p>
            </div>
          </div>

          {/* Column 3: Hours & Social */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-serif font-light mb-2 text-[#8b5d53]/90">Hours</h3>
              <div className="space-y-2.5 text-stone-600 font-light font-serif pt-8">
                <div className="flex justify-between items-center">
                  <span>Mon - Fri</span>
                  <span>10:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Saturday</span>
                  <span>10:00 AM - 3:00 PM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Sunday</span>
                  <span>Closed</span>
                </div>
              </div>
            </div>

            <div className="pt-4">
              <h3 className="text-lg font-serif font-light mb-4 text-[#8b5d53]/90">Follow Us</h3>
              <div className="flex space-x-3 pt-4">
                <Link 
                  href="https://www.instagram.com/glowbybry/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-[#8b5d53]/70 hover:text-[#8b5d53] transition-colors bg-white/60 hover:bg-white/80 p-2.5 rounded-full shadow-sm"
                >
                  <InstagramIcon className="w-4 h-4" />
                </Link>
                <Link 
                  href="https://www.facebook.com/people/Glow-by-Bry/61551419083452/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-[#8b5d53]/70 hover:text-[#8b5d53] transition-colors bg-white/60 hover:bg-white/80 p-2.5 rounded-full shadow-sm"
                >
                  <FacebookIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-[#8b5d53]/20 mt-6 pt-5 text-center">
          <p className="text-[#8b5d53]/70 font-light font-serif text-sm">&copy; {currentYear} GlowByBry. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}

export default SiteFooter;