'use server';

/**
 * Email Service Utility
 *
 * This utility provides functions for sending emails using Brevo.com email service.
 * It's designed to provide reliable email delivery for booking confirmations and notifications.
 */

import * as SibApiV3Sdk from '@getbrevo/brevo';

interface EmailData {
  to: string;
  subject: string;
  body: string;
  from?: string;
  replyTo?: string;
  params?: Record<string, string | number | boolean | null | undefined>; // Dynamic parameters for Brevo templates
  templateId?: number; // Optional Brevo template ID
}

/**
 * Send an email using Brevo service
 */
export async function sendEmail(data: EmailData): Promise<{ success: boolean; message: string }> {
  try {
    // Validate required fields
    if (!data.to || !data.subject) {
      console.error('Missing required email fields:', { to: !!data.to, subject: !!data.subject });
      return {
        success: false,
        message: 'Missing required email fields'
      };
    }

    // Log email data for debugging (excluding sensitive info)
    console.log('Sending email:', {
      to: data.to.substring(0, 3) + '***@' + data.to.split('@')[1],
      subject: data.subject,
      templateId: data.templateId || 'None',
      hasParams: !!data.params
    });
    
    // Check if environment variables are set - try both formats (with and without NEXT_PUBLIC_)
    const apiKey = process.env.BREVO_API_KEY || process.env.NEXT_PUBLIC_BREVO_API_KEY;
    if (!apiKey) {
      console.error('Brevo API key is not configured', {
        hasBrevoApiKey: !!process.env.BREVO_API_KEY,
        hasNextPublicBrevoApiKey: !!process.env.NEXT_PUBLIC_BREVO_API_KEY,
        environment: process.env.NODE_ENV
      });
      return { 
        success: false, 
        message: 'Email service is not properly configured. Please contact support.' 
      };
    }

    // Configure Brevo API client
    const apiInstance = new SibApiV3Sdk.TransactionalEmailsApi();
    apiInstance.setApiKey(SibApiV3Sdk.TransactionalEmailsApiApiKeys.apiKey, apiKey);
    
    // Create send email request
    const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
    
    sendSmtpEmail.subject = data.subject;
    
    // If a template ID is provided, use it, otherwise use the HTML content directly
    if (data.templateId) {
      sendSmtpEmail.templateId = data.templateId;
      sendSmtpEmail.params = data.params || {};
    } else {
      if (!data.body) {
        console.error('Email body is required when not using a template');
        return {
          success: false,
          message: 'Email content is missing'
        };
      }
      sendSmtpEmail.htmlContent = data.body;
    }
    
    sendSmtpEmail.sender = {
      name: "GlowByBry",
      email: data.from || "<EMAIL>"
    };
    
    sendSmtpEmail.to = [{
      email: data.to,
      name: data.to.split('@')[0] // Extract name from email as a fallback
    }];
    
    if (data.replyTo) {
      sendSmtpEmail.replyTo = {
        email: data.replyTo,
        name: "GlowByBry"
      };
    }
    
    // Extract booking reference if it's in the subject
    const referenceMatch = data.subject.match(/GBB-[A-Z0-9]+/);
    const bookingReference = referenceMatch ? referenceMatch[0] : "";
    
    // Add custom headers for tracking
    sendSmtpEmail.headers = {
      "X-Mailin-Tag": "booking-confirmation",
      ...(bookingReference ? { "X-Booking-Reference": bookingReference } : {})
    };

    // Send the email
    const result = await apiInstance.sendTransacEmail(sendSmtpEmail);
    
    console.log('Email sent successfully:', {
      response: result?.response?.statusCode,
      responseText: result?.response ? 'Response received' : 'No response details available',
      to: data.to.substring(0, 3) + '***@' + data.to.split('@')[1],
      subject: data.subject,
      templateId: data.templateId
    });

    return {
      success: true,
      message: 'Email sent successfully'
    };
  } catch (error) {
    // Log detailed error information
    console.error('Error sending email:', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      to: data.to?.substring(0, 3) + '***@' + data.to?.split('@')[1],
      subject: data.subject,
      templateId: data.templateId
    });

    // Return user-friendly error message
    return { 
      success: false, 
      message: error instanceof Error 
        ? (error.message.includes('api-key') 
          ? 'Email service authentication failed' 
          : 'Failed to send email. Please try again later.')
        : 'Unknown error occurred'
    };
  }
}

/**
 * Generate a booking confirmation email HTML
 * Note: This is used as a fallback if no Brevo template is available
 */
export async function generateBookingConfirmationEmail(booking: {
  reference: string;
  service: string;
  date: string;
  time: string;
  name: string;
  signature?: string;
  signatureDate?: string;
  bookingFee?: number;
  serviceDetails?: {
    price?: string | number;
    duration?: string;
    description?: string;
  };
}): Promise<string> {
  const formattedDate = new Date(booking.date).toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });

  return `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="color-scheme" content="light dark" />
        <meta name="supported-color-schemes" content="light dark" />
        <title>Booking Confirmation - Glow by Bry</title>
        <!--[if mso]>
        <style type="text/css">
          table {border-collapse: collapse; border-spacing: 0; margin: 0;}
          div, td {padding: 0;}
          div {margin: 0 !important;}
        </style>
        <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
        </noscript>
        <![endif]-->
        <style type="text/css">
            /* BASE STYLES */
            body {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                -webkit-text-size-adjust: 100% !important;
                -ms-text-size-adjust: 100% !important;
                -webkit-font-smoothing: antialiased !important;
            }
            img {
                border: 0 !important;
                outline: none !important;
                display: block !important;
                height: auto !important;
            }
            table, td {
                border-collapse: collapse !important;
            }
            #bodyTable {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }
            
            /* DARK MODE STYLES */
            @media (prefers-color-scheme: dark) {
                body, #bodyTable, .darkmode-bg {
                    background-color: #1c1c1e !important;
                }
                .darkmode-text {
                    color: #f2f2f7 !important;
                }
                .darkmode-subtext {
                    color: #d1d1d6 !important;
                }
                .darkmode-header {
                    background-color: #2c2c2e !important;
                    border-color: #3a3a3c !important;
                }
                .darkmode-content {
                    background-color: #2c2c2e !important; 
                    border-color: #3a3a3c !important;
                }
                .darkmode-footer {
                    background-color: #2c2c2e !important;
                    color: #d1d1d6 !important;
                }
                .darkmode-link {
                    color: #0a84ff !important;
                }
            }
            
            /* RESPONSIVE STYLES */
            @media only screen and (max-width: 480px) {
                .mobile-full-width {
                    width: 100% !important;
                }
                .mobile-padding {
                    padding-left: 10px !important;
                    padding-right: 10px !important;
                }
                .mobile-center {
                    text-align: center !important;
                }
                .mobile-stack {
                    display: block !important;
                    width: 100% !important;
                }
                .mobile-logo {
                    max-width: 220px !important;
                }
                .hide-mobile {
                    display: none !important;
                }
            }
        </style>
    </head>
    <body bgcolor="#f9f9f9" style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; color: #4a332e;" class="darkmode-bg">
        <!-- Preheader text -->
        <div style="display: none; max-height: 0px; overflow: hidden;">
            Your Glow by Bry appointment is confirmed! ${booking.service} on ${formattedDate} at ${booking.time}. Reference: ${booking.reference}
        </div>
        
        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" id="bodyTable" class="darkmode-bg">
            <tr>
                <td align="center" valign="top" style="padding: 20px 10px;">
                    <!-- Main wrapper table -->
                    <table class="mobile-full-width" width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#f8f1ee" style="border-radius: 10px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.05); max-width: 600px; width: 100%;" class="darkmode-content">
                        <!-- Header with Logo -->
                        <tr>
                            <td align="center" bgcolor="#f0e6e4" style="padding: 20px;" class="darkmode-header">
                                <a href="https://glowbybry.com" target="_blank">
                                    <img src="https://res.cloudinary.com/streamvista/image/upload/v1747274238/bryanna-logo_om6vxx.png" alt="Glow by Bry Logo" width="300" class="mobile-logo" style="max-width: 300px; width: 100%; height: auto; display: block; filter: brightness(120%);">
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Booking Reference -->
                        <tr>
                            <td bgcolor="#f0e6e4" style="padding: 10px 15px; text-align: center; border-top: 1px solid #e8d0c9;" class="darkmode-header">
                                <p style="font-size: 14px; color: #8b5d53; margin: 0; text-transform: uppercase; letter-spacing: 1px;" class="darkmode-subtext">BOOKING REFERENCE</p>
                                <p style="font-size: 22px; font-weight: bold; color: #4a332e; margin: 0; letter-spacing: 1px;" class="darkmode-text">${booking.reference}</p>
                            </td>
                        </tr>
                        
                        <!-- Content -->
                        <tr>
                            <td class="mobile-padding" style="padding: 25px 20px;">
                                <p style="font-size: 16px; color: #4a332e; line-height: 1.6; margin: 0 0 15px;" class="darkmode-text">Hello <span style="font-weight: 600;">${booking.name}</span>,</p>
                                <p style="font-size: 16px; color: #4a332e; line-height: 1.6; margin: 0 0 20px;" class="darkmode-text">Thank you for choosing Glow by Bry! We're thrilled to help you shine. Below are the details of your upcoming appointment:</p>
                                
                                <!-- Appointment Details Box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td style="background-color: #ffffff; border: 1px solid #e8d0c9; border-radius: 8px; overflow: hidden;" class="darkmode-content">
                                            <!-- Header -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td bgcolor="#f0e6e4" style="padding: 12px 15px; border-bottom: 1px solid #e8d0c9;" class="darkmode-header">
                                                        <h2 style="margin: 0; font-size: 18px; color: #8b5d53; font-weight: 600;" class="darkmode-text">Appointment Details</h2>
                                                    </td>
                                                </tr>
                                            </table>
                                            
                                            <!-- Details -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td style="padding: 15px;">
                                                        <table width="100%" border="0" cellspacing="0" cellpadding="5">
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Service:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.service}</td>
                                                            </tr>
                                                            ${booking.serviceDetails?.description ? `
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Description:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.serviceDetails.description}</td>
                                                            </tr>
                                                            ` : ''}
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Date:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${formattedDate}</td>
                                                            </tr>
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Time:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.time}</td>
                                                            </tr>
                                                            ${booking.serviceDetails?.duration ? `
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Duration:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.serviceDetails.duration}</td>
                                                            </tr>
                                                            ` : ''}
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Location:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">20 Lexington Avenue, London, EC1R 3HR</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                
                                <!-- Payment Information Box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td style="background-color: #ffffff; border: 1px solid #e8d0c9; border-radius: 8px; overflow: hidden;" class="darkmode-content">
                                            <!-- Header -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td bgcolor="#f0e6e4" style="padding: 12px 15px; border-bottom: 1px solid #e8d0c9;" class="darkmode-header">
                                                        <h2 style="margin: 0; font-size: 18px; color: #8b5d53; font-weight: 600;" class="darkmode-text">Payment Details</h2>
                                                    </td>
                                                </tr>
                                            </table>
                                            
                                            <!-- Details -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td style="padding: 15px;">
                                                        <table width="100%" border="0" cellspacing="0" cellpadding="5">
                                                            <tr>
                                                                <td width="60%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Booking Fee Paid:</strong></td>
                                                                <td width="40%" valign="top" style="font-size: 16px; color: #4a332e; text-align: right;" class="darkmode-text">$${booking.bookingFee || 25}</td>
                                                            </tr>
                                                            ${booking.serviceDetails?.price ? `
                                                            <tr>
                                                                <td width="60%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Total Price:</strong></td>
                                                                <td width="40%" valign="top" style="font-size: 16px; color: #4a332e; text-align: right;" class="darkmode-text">$${booking.serviceDetails.price}</td>
                                                            </tr>
                                                            <tr>
                                                                <td width="60%" valign="top" style="font-size: 16px; color: #8b5d53; border-top: 1px solid #e8d0c9; padding-top: 10px;" class="darkmode-subtext"><strong>Balance Due at Appointment:</strong></td>
                                                                <td width="40%" valign="top" style="font-size: 16px; color: #4a332e; text-align: right; border-top: 1px solid #e8d0c9; padding-top: 10px; font-weight: 600;" class="darkmode-text">$${
                                                                  typeof booking.serviceDetails.price === 'string' && booking.serviceDetails.price.includes('-')
                                                                    ? 'Varies based on service'
                                                                    : Number(booking.serviceDetails.price) - (booking.bookingFee || 25)
                                                                }</td>
                                                            </tr>
                                                            ` : ''}
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                
                                <!-- Important Note -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td bgcolor="#f0e6e4" style="border-radius: 8px; padding: 15px;" class="darkmode-content">
                                            <p style="font-size: 16px; color: #4a332e; line-height: 1.5; margin: 0;" class="darkmode-text">
                                                <strong>Need to reschedule?</strong> No problem! Please give us 24 hours notice by replying to this email or calling <a href="tel:+441234567890" style="color: #b07c70; text-decoration: none; font-weight: bold;" class="darkmode-link">(+44) 1234 567890</a>.
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td bgcolor="#f0e6e4" class="mobile-padding darkmode-footer" style="padding: 25px 20px; text-align: center; font-size: 14px; color: #6a4840;" class="darkmode-text">
                                <p style="margin: 0 0 10px;" class="darkmode-text">20 Lexington Avenue, London, EC1R 3HR</p>
                                <p style="margin: 0 0 10px;">
                                    <a href="mailto:<EMAIL>" style="color: #8b5d53; text-decoration: none; font-weight: bold;" class="darkmode-link"><EMAIL></a> | 
                                    <a href="tel:+441234567890" style="color: #8b5d53; text-decoration: none; font-weight: bold;" class="darkmode-link">(+44) 1234 567890</a>
                                </p>
                                <p style="margin: 0 0 10px;">Monday - Friday: 10:00 AM - 5:00 PM<br>Saturday: 10:00 AM - 3:00 PM<br>Sunday: Closed</p>
                                <p style="margin: 0; font-size: 12px;" class="darkmode-text">© ${new Date().getFullYear()} Glow by Bry. All rights reserved.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
  `;
}

/**
 * Send consent form as a separate email
 */
export async function sendConsentForm(booking: {
  reference: string;
  service: string;
  name: string;
  email: string;
  signature: string;
  signatureDate?: string;
  consentAgreed?: boolean;
  bookingFee?: number;
  serviceDetails?: {
    price?: string | number;
    duration?: string;
    description?: string;
  };
}): Promise<{ success: boolean; message: string }> {
  // Try to use Brevo template if configured
  const templateId = process.env.BREVO_CONSENT_TEMPLATE_ID || process.env.NEXT_PUBLIC_BREVO_CONSENT_TEMPLATE_ID;
  const CONSENT_TEMPLATE_ID = templateId ? Number(templateId) : undefined;
  
  if (CONSENT_TEMPLATE_ID) {
    return await sendEmail({
      to: '<EMAIL>', // Send to admin email
      subject: `Signed Consent Form - ${booking.reference} - ${booking.name}`,
      body: '', // Body not needed when using template
      from: '<EMAIL>',
      replyTo: booking.email,
      templateId: CONSENT_TEMPLATE_ID,
      params: {
        reference: booking.reference,
        service: booking.service,
        serviceDescription: booking.serviceDetails?.description || '',
        serviceDuration: booking.serviceDetails?.duration || '',
        servicePrice: booking.serviceDetails?.price || '',
        name: booking.name,
        email: booking.email,
        signature: booking.signature,
        signatureDate: booking.signatureDate || new Date().toLocaleDateString(),
        consentAgreed: booking.consentAgreed || false,
        bookingFee: booking.bookingFee || 25,
        // Business information
        businessName: 'Glow by Bry',
        businessEmail: '<EMAIL>',
        businessPhone: '(*************',
        businessAddress: '77935 Calle Tampico #103, La Quinta, CA 92253',
        businessHours: 'Monday - Friday: 10:00 AM - 5:00 PM<br>Saturday: 10:00 AM - 3:00 PM<br>Sunday: Closed',
        // Current year for copyright
        currentYear: new Date().getFullYear()
      }
    });
  }
  
  // Generate the consent form email body as fallback using responsive design
  const consentEmailBody = `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="color-scheme" content="light dark" />
        <meta name="supported-color-schemes" content="light dark" />
        <title>Signed Consent Form - Glow by Bry</title>
        <!--[if mso]>
        <style type="text/css">
          table {border-collapse: collapse; border-spacing: 0; margin: 0;}
          div, td {padding: 0;}
          div {margin: 0 !important;}
        </style>
        <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
        </noscript>
        <![endif]-->
        <style type="text/css">
            /* BASE STYLES */
            body {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                -webkit-text-size-adjust: 100% !important;
                -ms-text-size-adjust: 100% !important;
                -webkit-font-smoothing: antialiased !important;
            }
            img {
                border: 0 !important;
                outline: none !important;
                display: block !important;
                height: auto !important;
            }
            table, td {
                border-collapse: collapse !important;
            }
            #bodyTable {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
            }
            
            /* DARK MODE STYLES */
            @media (prefers-color-scheme: dark) {
                body, #bodyTable, .darkmode-bg {
                    background-color: #1c1c1e !important;
                }
                .darkmode-text {
                    color: #f2f2f7 !important;
                }
                .darkmode-subtext {
                    color: #d1d1d6 !important;
                }
                .darkmode-header {
                    background-color: #2c2c2e !important;
                    border-color: #3a3a3c !important;
                }
                .darkmode-content {
                    background-color: #2c2c2e !important; 
                    border-color: #3a3a3c !important;
                }
                .darkmode-footer {
                    background-color: #2c2c2e !important;
                    color: #d1d1d6 !important;
                }
                .darkmode-link {
                    color: #0a84ff !important;
                }
                .signature-box {
                    background-color: #2c2c2e !important;
                    border-color: #3a3a3c !important;
                }
            }
            
            /* RESPONSIVE STYLES */
            @media only screen and (max-width: 480px) {
                .mobile-full-width {
                    width: 100% !important;
                }
                .mobile-padding {
                    padding-left: 10px !important;
                    padding-right: 10px !important;
                }
                .mobile-center {
                    text-align: center !important;
                }
                .mobile-stack {
                    display: block !important;
                    width: 100% !important;
                }
                .mobile-logo {
                    max-width: 220px !important;
                }
                .hide-mobile {
                    display: none !important;
                }
            }
        </style>
    </head>
    <body bgcolor="#f9f9f9" style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; color: #4a332e;" class="darkmode-bg">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" id="bodyTable" class="darkmode-bg">
            <tr>
                <td align="center" valign="top" style="padding: 20px 10px;">
                    <!-- Main wrapper table -->
                    <table class="mobile-full-width" width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#f8f1ee" style="border-radius: 10px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.05); max-width: 600px; width: 100%;" class="darkmode-content">
                        <!-- Header with Logo -->
                        <tr>
                            <td align="center" bgcolor="#f0e6e4" style="padding: 20px;" class="darkmode-header">
                                <a href="https://glowbybry.com" target="_blank">
                                    <img src="https://res.cloudinary.com/streamvista/image/upload/v1747274238/bryanna-logo_om6vxx.png" alt="Glow by Bry Logo" width="300" class="mobile-logo" style="max-width: 300px; width: 100%; height: auto; display: block; filter: brightness(120%);">
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Document Title -->
                        <tr>
                            <td bgcolor="#f0e6e4" style="padding: 10px 15px; text-align: center; border-top: 1px solid #e8d0c9;" class="darkmode-header">
                                <p style="font-size: 14px; color: #8b5d53; margin: 0; text-transform: uppercase; letter-spacing: 1px;" class="darkmode-subtext">SIGNED CONSENT FORM</p>
                                <p style="font-size: 22px; font-weight: bold; color: #4a332e; margin: 0; letter-spacing: 1px;" class="darkmode-text">${booking.reference}</p>
                            </td>
                        </tr>
                        
                        <!-- Content -->
                        <tr>
                            <td class="mobile-padding" style="padding: 25px 20px;">
                                <!-- Client Information Box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td style="background-color: #ffffff; border: 1px solid #e8d0c9; border-radius: 8px; overflow: hidden;" class="darkmode-content">
                                            <!-- Header -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td bgcolor="#f0e6e4" style="padding: 12px 15px; border-bottom: 1px solid #e8d0c9;" class="darkmode-header">
                                                        <h2 style="margin: 0; font-size: 18px; color: #8b5d53; font-weight: 600;" class="darkmode-text">Client Information</h2>
                                                    </td>
                                                </tr>
                                            </table>
                                            
                                            <!-- Details -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td style="padding: 15px;">
                                                        <table width="100%" border="0" cellspacing="0" cellpadding="5">
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Name:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.name}</td>
                                                            </tr>
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Email:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.email}</td>
                                                            </tr>
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Service:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.service}</td>
                                                            </tr>
                                                            ${booking.serviceDetails?.description ? `
                                                            <tr>
                                                                <td width="30%" valign="top" style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Description:</strong></td>
                                                                <td width="70%" valign="top" style="font-size: 16px; color: #4a332e;" class="darkmode-text">${booking.serviceDetails.description}</td>
                                                            </tr>
                                                            ` : ''}
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                
                                <!-- Consent Form Content -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td style="background-color: #ffffff; border: 1px solid #e8d0c9; border-radius: 8px; overflow: hidden;" class="darkmode-content">
                                            <!-- Header -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td bgcolor="#f0e6e4" style="padding: 12px 15px; border-bottom: 1px solid #e8d0c9;" class="darkmode-header">
                                                        <h2 style="margin: 0; font-size: 18px; color: #8b5d53; font-weight: 600;" class="darkmode-text">Consent Form & Liability Waiver</h2>
                                                    </td>
                                                </tr>
                                            </table>
                                            
                                            <!-- Content -->
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td style="padding: 15px;">
                                                        <p style="font-size: 16px; color: #4a332e; line-height: 1.6; margin: 0 0 15px;" class="darkmode-text">
                                                            I hereby consent to receive the beauty treatment listed above. I confirm that I have been informed about the treatment, 
                                                            its process, potential risks, and aftercare requirements. I understand that results may vary and that all efforts will 
                                                            be made to achieve the best possible outcome.
                                                        </p>
                                                        
                                                        <p style="font-size: 16px; color: #4a332e; line-height: 1.6; margin: 0 0 15px;" class="darkmode-text">
                                                            I acknowledge that I have disclosed all relevant health information and allergies that might affect the treatment.
                                                            I understand that I must follow all aftercare instructions to achieve optimal results and prevent complications.
                                                        </p>
                                                        
                                                        <p style="font-size: 16px; color: #4a332e; line-height: 1.6; margin: 0 0 15px;" class="darkmode-text">
                                                            I agree that GlowByBry and its practitioners are not liable for any unforeseen reactions or results that might occur 
                                                            despite proper application of the treatment. I understand that I am responsible for reporting any adverse reactions 
                                                            promptly and seeking medical attention if necessary.
                                                        </p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                                
                                <!-- Signature Box -->
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 20px;">
                                    <tr>
                                        <td style="background-color: #ffffff; border: 1px solid #e8d0c9; border-radius: 8px; overflow: hidden;" class="darkmode-content">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                <tr>
                                                    <td style="padding: 15px;">
                                                        <p style="font-size: 16px; color: #8b5d53; margin: 0 0 10px;" class="darkmode-subtext"><strong>Client Signature:</strong></p>
                                                        <div style="border: 1px solid #e8d0c9; padding: 10px; background-color: white; margin-bottom: 15px;" class="signature-box">
                                                            <img src="${booking.signature}" style="max-width: 100%; height: auto;" alt="Client Signature" />
                                                        </div>
                                                        <p style="font-size: 16px; color: #8b5d53;" class="darkmode-subtext"><strong>Date:</strong> <span style="color: #4a332e;" class="darkmode-text">${booking.signatureDate || new Date().toLocaleDateString()}</span></p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td bgcolor="#f0e6e4" class="mobile-padding darkmode-footer" style="padding: 25px 20px; text-align: center; font-size: 14px; color: #6a4840;">
                                <p style="margin: 0 0 10px;" class="darkmode-text">This document serves as a legally binding consent form.</p>
                                <p style="margin: 0; font-size: 12px;" class="darkmode-text">© ${new Date().getFullYear()} Glow by Bry. All rights reserved.</p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
  `;

  return await sendEmail({
    to: '<EMAIL>', // Send to admin email
    subject: `Signed Consent Form - ${booking.reference} - ${booking.name}`,
    body: consentEmailBody,
    from: '<EMAIL>',
    replyTo: booking.email
  });
}

/**
 * Send a booking confirmation email
 */
export async function sendBookingConfirmation(booking: {
  reference: string;
  service: string;
  date: string;
  time: string;
  name: string;
  email: string;
  phone?: string;
  signature?: string;
  signatureDate?: string;
  consentAgreed?: boolean;
  bookingFee?: number;
  serviceDetails?: {
    price?: string | number;
    duration?: string;
    description?: string;
  };
}): Promise<{ success: boolean; message: string }> {
  try {
    // Validate required fields
    if (!booking.email || !booking.reference || !booking.service || !booking.date || !booking.time || !booking.name) {
      console.error('Missing required booking fields:', {
        hasEmail: !!booking.email,
        hasReference: !!booking.reference,
        hasService: !!booking.service,
        hasDate: !!booking.date,
        hasTime: !!booking.time,
        hasName: !!booking.name
      });
      return {
        success: false,
        message: 'Missing required booking information'
      };
    }

    // Format date for display and params
    const formattedDate = new Date(booking.date).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
    
    // Get template ID from environment variables
    const templateIdStr = process.env.BREVO_BOOKING_TEMPLATE_ID || process.env.NEXT_PUBLIC_BREVO_BOOKING_TEMPLATE_ID;
    const templateId = templateIdStr ? Number(templateIdStr) : 1; // Default to 1 if not provided
    
    // Get admin notification template ID (separate template for business emails)
    const adminTemplateIdStr = process.env.BREVO_ADMIN_BOOKING_TEMPLATE_ID || process.env.NEXT_PUBLIC_BREVO_ADMIN_BOOKING_TEMPLATE_ID;
    const adminTemplateId = adminTemplateIdStr ? Number(adminTemplateIdStr) : undefined;
    
    // Parameters for template
    const emailParams = {
      reference: booking.reference,
      service: booking.service,
      serviceDescription: booking.serviceDetails?.description || '',
      serviceDuration: booking.serviceDetails?.duration || '',
      servicePrice: booking.serviceDetails?.price || '',
      date: booking.date,
      formattedDate: formattedDate,
      time: booking.time,
      name: booking.name,
      email: booking.email,
      phone: booking.phone || 'Not provided',
      bookingFee: booking.bookingFee || 25,
      balanceDue: typeof booking.serviceDetails?.price === 'string' && String(booking.serviceDetails?.price).includes('-')
        ? 'Varies based on service'
        : Number(booking.serviceDetails?.price || 0) - (booking.bookingFee || 25),
      // Featured services for "Elevate Your Glow Experience" section
      featuredService1Name: "Classic Facial",
      featuredService1Price: "$85",
      featuredService2Name: "Lash Lift & Tint",
      featuredService2Price: "$95",
      featuredService3Name: "Brow Lamination",
      featuredService3Price: "$75",
      featuredService4Name: "Dermaplaning",
      featuredService4Price: "$65",
      // Business information
      businessName: 'Glow by Bry',
      businessEmail: '<EMAIL>',
      businessPhone: '(*************',
      businessAddress: '77935 Calle Tampico #103, La Quinta, CA 92253',
      businessHours: 'Monday - Friday: 10:00 AM - 5:00 PM<br>Saturday: 10:00 AM - 3:00 PM<br>Sunday: Closed',
      // Current year for copyright
      currentYear: new Date().getFullYear()
    };
    
    // Send the customer confirmation email using template
    const customerEmailResult = await sendEmail({
      to: booking.email,
      subject: `GlowByBry Booking Confirmation: ${booking.reference}`,
      body: '', // Not needed when using template
      from: '<EMAIL>',
      replyTo: '<EMAIL>',
      templateId: templateId,
      params: emailParams
    });

    if (!customerEmailResult.success) {
      console.error('Failed to send customer confirmation email:', customerEmailResult.message);
      return customerEmailResult;
    }

    // Send a differently formatted email to the business
    if (adminTemplateId) {
      // If a specific admin template exists, use it
      const businessEmailResult = await sendEmail({
        to: '<EMAIL>',
        subject: `🔔 NEW BOOKING: ${booking.reference} - ${booking.service}`,
        body: '',
        from: '<EMAIL>',
        replyTo: booking.email,
        templateId: adminTemplateId,
        params: emailParams
      });

      if (!businessEmailResult.success) {
        console.warn('Failed to send business notification email:', businessEmailResult.message);
        // Don't fail the whole operation if only the business copy fails
      }
    } else {
      // If no specific admin template exists, create a compact admin-friendly HTML email
      const businessEmailBody = generateAdminBookingNotification(booking, formattedDate);
      const businessEmailResult = await sendEmail({
        to: '<EMAIL>',
        subject: `🔔 NEW BOOKING: ${booking.reference} - ${booking.service}`,
        body: businessEmailBody,
        from: '<EMAIL>',
        replyTo: booking.email
      });

      if (!businessEmailResult.success) {
        console.warn('Failed to send business notification email:', businessEmailResult.message);
      }
    }

    return {
      success: true,
      message: 'Booking confirmation sent successfully'
    };
  } catch (error) {
    console.error('Error in sendBookingConfirmation:', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      bookingRef: booking.reference,
      service: booking.service
    });
    
    return {
      success: false,
      message: 'Failed to send booking confirmation. Please try again later.'
    };
  }
}

/**
 * Generate a compact, admin-friendly booking notification email
 * This format is optimized for quickly scanning booking details
 */
function generateAdminBookingNotification(booking: {
  reference: string;
  service: string;
  date: string;
  time: string;
  name: string;
  email: string;
  phone?: string;
  bookingFee?: number;
  serviceDetails?: {
    price?: string | number;
    duration?: string;
    description?: string;
  };
}, formattedDate: string): string {
  const balanceDue = typeof booking.serviceDetails?.price === 'string' && String(booking.serviceDetails?.price).includes('-')
    ? 'Varies based on service'
    : `$${Number(booking.serviceDetails?.price || 0) - (booking.bookingFee || 25)}`;
    
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Booking Notification</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }
            .notification-box {
                background-color: white;
                border: 1px solid #ddd;
                border-left: 4px solid #b07c70;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 4px;
            }
            .header {
                background-color: #b07c70;
                color: white;
                padding: 10px 15px;
                margin-bottom: 20px;
                border-radius: 4px;
                text-align: center;
            }
            .booking-info {
                margin-bottom: 20px;
            }
            .booking-detail {
                display: flex;
                margin-bottom: 8px;
                border-bottom: 1px solid #eee;
                padding-bottom: 8px;
            }
            .booking-label {
                font-weight: bold;
                width: 140px;
                color: #555;
            }
            .booking-value {
                flex: 1;
            }
            .reference {
                font-family: Consolas, Monaco, 'Courier New', monospace;
                background-color: #f1f1f1;
                padding: 2px 5px;
                border-radius: 3px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            .price-info {
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 4px;
                margin-top: 15px;
            }
            .calendar-btn {
                display: inline-block;
                background-color: #4285f4;
                color: white;
                padding: 8px 16px;
                text-decoration: none;
                border-radius: 4px;
                margin-top: 10px;
                font-weight: bold;
            }
            .calendar-btn:hover {
                background-color: #3367d6;
            }
            .action-section {
                margin-top: 20px;
                text-align: center;
            }
            @media (max-width: 500px) {
                .booking-detail {
                    flex-direction: column;
                }
                .booking-label {
                    width: 100%;
                    margin-bottom: 4px;
                }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1 style="margin: 0; font-size: 22px;">New Booking Notification</h1>
        </div>
        
        <div class="notification-box">
            <p style="color: #666; text-align: center; margin-top: 0;">
                A new booking has been made on your website.
            </p>
            
            <div class="booking-info">
                <div class="booking-detail">
                    <div class="booking-label">Reference:</div>
                    <div class="booking-value"><span class="reference">${booking.reference}</span></div>
                </div>
                
                <div class="booking-detail">
                    <div class="booking-label">Service:</div>
                    <div class="booking-value"><strong>${booking.service}</strong></div>
                </div>
                
                <div class="booking-detail">
                    <div class="booking-label">Date & Time:</div>
                    <div class="booking-value">${formattedDate} at ${booking.time}</div>
                </div>
                
                ${booking.serviceDetails?.duration ? `
                <div class="booking-detail">
                    <div class="booking-label">Duration:</div>
                    <div class="booking-value">${booking.serviceDetails.duration}</div>
                </div>
                ` : ''}
                
                <div class="booking-detail">
                    <div class="booking-label">Client Name:</div>
                    <div class="booking-value">${booking.name}</div>
                </div>
                
                <div class="booking-detail">
                    <div class="booking-label">Email:</div>
                    <div class="booking-value">
                        <a href="mailto:${booking.email}" style="color: #b07c70;">${booking.email}</a>
                    </div>
                </div>
                
                ${booking.phone ? `
                <div class="booking-detail">
                    <div class="booking-label">Phone:</div>
                    <div class="booking-value">
                        <a href="tel:${booking.phone}" style="color: #b07c70;">${booking.phone}</a>
                    </div>
                </div>
                ` : ''}
                
                <div class="price-info">
                    <div class="booking-detail" style="border-bottom: none; padding-bottom: 0; margin-bottom: 4px;">
                        <div class="booking-label">Booking Fee Paid:</div>
                        <div class="booking-value">$${booking.bookingFee || 25}</div>
                    </div>
                    
                    ${booking.serviceDetails?.price ? `
                    <div class="booking-detail" style="border-bottom: none; padding-bottom: 0; margin-bottom: 4px;">
                        <div class="booking-label">Service Price:</div>
                        <div class="booking-value">$${booking.serviceDetails.price}</div>
                    </div>
                    
                    <div class="booking-detail" style="border-bottom: none; padding-bottom: 0;">
                        <div class="booking-label">Balance Due:</div>
                        <div class="booking-value"><strong>${balanceDue}</strong></div>
                    </div>
                    ` : ''}
                </div>
            </div>
            
            <div class="action-section">
                <p style="margin-bottom: 5px;"><strong>Add to your calendar:</strong></p>
                <a href="https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(`GBB: ${booking.name} - ${booking.service}`)}&dates=${encodeURIComponent(formatDateForCalendar(booking.date, booking.time))}&details=${encodeURIComponent(`Booking Reference: ${booking.reference}\nClient: ${booking.name}\nService: ${booking.service}\nEmail: ${booking.email}\nPhone: ${booking.phone || 'Not provided'}\n${booking.serviceDetails?.price ? `Price: $${booking.serviceDetails.price}` : ''}`)}" class="calendar-btn" target="_blank">
                    Add to Google Calendar
                </a>
            </div>
        </div>
        
        <p style="font-size: 12px; color: #888; text-align: center;">
            This is an automated notification from your booking system.
            <br>© ${new Date().getFullYear()} Glow by Bry
        </p>
    </body>
    </html>
  `;
}

/**
 * Format date and time for Google Calendar link
 */
function formatDateForCalendar(dateStr: string, timeStr: string): string {
  // Parse the date and time
  const date = new Date(dateStr);
  const [hours, minutes] = timeStr.split(':').map(Number);
  
  // Set the hours and minutes
  date.setHours(hours);
  date.setMinutes(minutes);
  
  // Calculate end time (assuming 1 hour duration if not specified)
  const endDate = new Date(date);
  endDate.setHours(date.getHours() + 1);
  
  // Format for Google Calendar
  // Format: YYYYMMDDTHHMMSSZ/YYYYMMDDTHHMMSSZ
  const formatDatePart = (d: Date) => {
    return d.getFullYear().toString() +
      (d.getMonth() + 1).toString().padStart(2, '0') +
      d.getDate().toString().padStart(2, '0') + 'T' +
      d.getHours().toString().padStart(2, '0') +
      d.getMinutes().toString().padStart(2, '0') +
      '00';
  };
  
  return formatDatePart(date) + '/' + formatDatePart(endDate);
}