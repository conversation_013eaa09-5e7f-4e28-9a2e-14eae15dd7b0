"use client";

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';

type Language = 'en' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  showLanguageModal: boolean;
  setShowLanguageModal: (show: boolean) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// English translations
const enTranslations: Record<string, string> = {
  // Navigation
  'nav.home': 'Home',
  'nav.services': 'Services',
  'nav.booking': 'Booking',
  'nav.contact': 'Contact',

  // Home Page
  'home.hero.title': 'Your Glow Up Starts Here',
  'home.hero.subtitle': 'Beauty services that enhance your natural glow',
  'home.hero.cta': 'Book Now',
  'home.about.title': 'About GlowByBry',
  'home.about.description': 'GlowByBry is a beauty spa dedicated to enhancing your natural beauty. We offer a range of services designed to help you look and feel your best.',
  'home.services.title': 'Our Services',
  'home.services.viewAll': 'View All Services',
  'home.testimonials.title': 'IT GIRLS SAY WHAT?',

  // Services Page
  'services.title': 'Discover Your Glow Ritual',
  'services.subtitle': 'Every ritual is a step closer to your ultimate glow.',
  'services.cta': 'Book This Service',

  // Booking Page
  'booking.title': 'Book Your Appointment',
  'booking.subtitle': 'Schedule your beauty treatment with GlowByBry and experience transformative beauty treatments that enhance your natural radiance.',
  'booking.step1': 'Select a Service',
  'booking.step2': 'Choose Date & Time',
  'booking.step3': 'Booking Agreement',
  'booking.step4': 'Your Information',
  'booking.step5': 'Sign Agreement',
  'booking.step6': 'Review & Confirm',
  'booking.next': 'Next',
  'booking.back': 'Back',
  'booking.confirm': 'Confirm Booking',
  'booking.success.title': 'Booking Confirmed!',
  'booking.success.message': 'Your appointment has been successfully booked. We look forward to seeing you!',

  // Contact Page
  'contact.title': 'Contact Us',
  'contact.subtitle': 'Have questions or ready to book your appointment? Reach out to us and we\'ll get back to you as soon as possible.',
  'contact.form.title': 'Send Us a Message',
  'contact.form.subtitle': 'Fill out the form below and we\'ll get back to you shortly.',
  'contact.form.firstName': 'First Name',
  'contact.form.lastName': 'Last Name',
  'contact.form.email': 'Email Address',
  'contact.form.phone': 'Phone Number',
  'contact.form.message': 'Message',
  'contact.form.submit': 'Send Message',
  'contact.success.title': 'Message Sent!',
  'contact.success.message': 'Thank you for contacting us. We\'ll get back to you as soon as possible.',
  'contact.info.location': 'Our Location',
  'contact.info.email': 'Email',
  'contact.info.phone': 'Phone',
  'contact.info.hours': 'Hours',

  // Language Modal
  'language.title': 'Choose Your Language',
  'language.subtitle': 'Select your preferred language for browsing our website',
  'language.english': 'English',
  'language.spanish': 'Spanish',
  'language.continue': 'Continue',
};

// Spanish translations
const esTranslations: Record<string, string> = {
  // Navigation
  'nav.home': 'Inicio',
  'nav.services': 'Servicios',
  'nav.booking': 'Reservas',
  'nav.contact': 'Contacto',

  // Home Page
  'home.hero.title': 'Tu Resplandor Comienza Aquí',
  'home.hero.subtitle': 'Servicios de belleza que realzan tu brillo natural',
  'home.hero.cta': 'Reservar Ahora',
  'home.about.title': 'Acerca de GlowByBry',
  'home.about.description': 'GlowByBry es un spa de belleza dedicado a realzar tu belleza natural. Ofrecemos una variedad de servicios diseñados para ayudarte a lucir y sentirte mejor.',
  'home.services.title': 'Nuestros Servicios',
  'home.services.viewAll': 'Ver Todos los Servicios',
  'home.testimonials.title': '¿QUÉ DICEN NUESTRAS CLIENTAS?',

  // Services Page
  'services.title': 'Descubre Tu Ritual de Brillo',
  'services.subtitle': 'Cada ritual es un paso más cerca de tu resplandor definitivo.',
  'services.cta': 'Reservar Este Servicio',

  // Booking Page
  'booking.title': 'Reserva Tu Cita',
  'booking.subtitle': 'Programa tu tratamiento de belleza con GlowByBry y experimenta tratamientos de belleza transformadores que realzan tu radiancia natural.',
  'booking.step1': 'Seleccionar un Servicio',
  'booking.step2': 'Elegir Fecha y Hora',
  'booking.step3': 'Acuerdo de Reserva',
  'booking.step4': 'Tu Información',
  'booking.step5': 'Firmar Acuerdo',
  'booking.step6': 'Revisar y Confirmar',
  'booking.next': 'Siguiente',
  'booking.back': 'Atrás',
  'booking.confirm': 'Confirmar Reserva',
  'booking.success.title': '¡Reserva Confirmada!',
  'booking.success.message': 'Tu cita ha sido reservada con éxito. ¡Esperamos verte pronto!',

  // Contact Page
  'contact.title': 'Contáctanos',
  'contact.subtitle': '¿Tienes preguntas o estás listo para reservar tu cita? Comunícate con nosotros y te responderemos lo antes posible.',
  'contact.form.title': 'Envíanos un Mensaje',
  'contact.form.subtitle': 'Completa el formulario a continuación y nos pondremos en contacto contigo pronto.',
  'contact.form.firstName': 'Nombre',
  'contact.form.lastName': 'Apellido',
  'contact.form.email': 'Correo Electrónico',
  'contact.form.phone': 'Número de Teléfono',
  'contact.form.message': 'Mensaje',
  'contact.form.submit': 'Enviar Mensaje',
  'contact.success.title': '¡Mensaje Enviado!',
  'contact.success.message': 'Gracias por contactarnos. Nos pondremos en contacto contigo lo antes posible.',
  'contact.info.location': 'Nuestra Ubicación',
  'contact.info.email': 'Correo Electrónico',
  'contact.info.phone': 'Teléfono',
  'contact.info.hours': 'Horario',

  // Language Modal
  'language.title': 'Elige Tu Idioma',
  'language.subtitle': 'Selecciona tu idioma preferido para navegar por nuestro sitio web',
  'language.english': 'Inglés',
  'language.spanish': 'Español',
  'language.continue': 'Continuar',
};

const translations: Record<Language, Record<string, string>> = {
  en: enTranslations,
  es: esTranslations,
};

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [showLanguageModal, setShowLanguageModal] = useState(false);

  // Load language preference from localStorage on initial render
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
      setLanguageState(savedLanguage);
    } else {
      // If no language preference is saved, show the language selection modal
      setShowLanguageModal(true);
    }
  }, []);

  // Save language preference to localStorage whenever it changes
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
  };

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, showLanguageModal, setShowLanguageModal }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
