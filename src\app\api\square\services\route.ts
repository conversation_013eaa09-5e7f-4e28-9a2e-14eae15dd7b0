import { NextResponse } from 'next/server';
import { catalogApi } from '../config';
import { CatalogObject } from 'square';

export async function GET() {
  try {
    console.log('Fetching services from Square Catalog...', {
      environment: process.env.NODE_ENV,
      hasToken: !!process.env.SQUARE_ACCESS_TOKEN,
      locationId: process.env.SQUARE_LOCATION_ID
    });

    // Fetch services from Square Catalog API
    const response = await catalogApi.searchCatalogItems({
      productTypes: ["APPOINTMENTS_SERVICE"],
      limit: 100
    });

    console.log('Square API Response:', {
      status: response.statusCode,
      itemCount: response.result.items?.length || 0,
      items: response.result.items?.map(item => ({
        id: item.id,
        type: item.type,
        itemData: {
          name: item.itemData?.name,
          variations: item.itemData?.variations?.length || 0
        }
      }))
    });

    // Define standard durations based on service names
    // This is a fallback approach since the API durations appear to be in a unit we can't easily convert
    const standardDurations: { [key: string]: string } = {
      "A La Carte": "30 min",
      "Collagen Induction": "90 min",
      "Customized Facials": "60 min",
      "Lash Lift": "60 min",
      "The Glow Edit": "90 min",
      "Waxing services": "30-60 min"
    };

    // Transform the data to match our frontend needs
    const services = response.result.items?.map((item: CatalogObject) => {
      const itemData = item.itemData;
      const variation = itemData?.variations?.[0];
      const variationData = variation?.itemVariationData;
      const priceAmount = variationData?.priceMoney?.amount;
      const serviceName = itemData?.name || '';
      
      // Convert milliseconds to human-readable format
      function formatDuration(durationMs: number): string {
        const minutes = durationMs / 60000; // Convert to minutes
        
        if (minutes < 60) {
          return `${minutes} min`;
        } else {
          const hours = Math.floor(minutes / 60);
          const remainingMinutes = minutes % 60;
          
          if (remainingMinutes === 0) {
            return `${hours} hour${hours > 1 ? 's' : ''}`;
          } else {
            return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} min`;
          }
        }
      }

      // Determine if this is a variable duration service based on name
      const hasVariableDuration = variationData?.name?.toLowerCase().includes('variable') ||
        itemData?.name?.toLowerCase().includes('waxing');

      // Format the duration
      const rawDuration = variationData?.serviceDuration;
      console.log(`Raw duration for ${variationData?.name}: ${rawDuration}`);

      const formattedDuration = rawDuration 
        ? formatDuration(Number(rawDuration)) 
        : hasVariableDuration 
          ? '30-60 min'  // Default for variable services
          : '60 min';    // Default duration
      
      console.log(`Using standard duration for ${serviceName}: ${formattedDuration}`);

      const service = {
        id: variation?.id || '',
        name: serviceName,
        description: itemData?.description || '',
        // If price is 0 or undefined, mark it as "Variable"
        price: priceAmount ? Number(priceAmount) / 100 : 'Variable',
        duration: formattedDuration
      };

      console.log('Transformed service:', service);
      return service;
    }) || [];

    return NextResponse.json({
      status: 'success',
      services
    });

  } catch (error) {
    console.error('Square API Error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to fetch services',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 