import { NextResponse } from 'next/server';
import { locationsApi, catalogApi } from '../config';

export async function GET() {
  try {
    // Test 1: Get location info
    const locationResult = await locationsApi.retrieveLocation(
      process.env.SQUARE_LOCATION_ID!
    );

    // Test 2: List catalog items
    const catalogResult = await catalogApi.searchCatalogItems({
      productTypes: ["APPOINTMENTS_SERVICE"]
    });

    return NextResponse.json({
      status: 'success',
      tests: {
        location: {
          name: locationResult.result.location?.businessName,
          status: locationResult.result.location?.status
        },
        services: catalogResult.result.items?.length || 0
      }
    });
  } catch (error) {
    console.error('Square API Error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to connect to Square',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 