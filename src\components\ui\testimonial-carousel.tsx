"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Quote } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";


import { cn } from "@/lib/utils";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface Testimonial {
  id: string;
  name: string;
  role?: string;
  content: string;
  image: string;
  rating?: number;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  title?: string;
  description?: string;
  className?: string;
}

export function TestimonialCarousel({
  testimonials,
  title,
  description,
  className
}: TestimonialCarouselProps) {
  const [isMounted, setIsMounted] = useState(false);

  // Use default values if no title/description provided
  const displayTitle = title || "IT GIRLS SAY WHAT?";
  const displayDescription = description || "Because the glow speaks, and so do our girls.";

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <section className={cn("py-16 relative overflow-hidden", className)}>
      {/* Background decoration */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-[#d4b2a7] opacity-10 blur-3xl" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl lg:text-5xl font-light tracking-wide mb-4 text-[#8b5d53] uppercase font-serif"
          >
            {displayTitle}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-lg text-[#5d3f39]/80 max-w-2xl mx-auto font-light"
          >
            {displayDescription}
          </motion.p>
          <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-6"></div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={testimonial.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="h-full">
                    <TestimonialCard testimonial={testimonial} index={index} />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex justify-center mt-8">
              <CarouselPrevious className="mr-2 relative translate-y-0 left-0" />
              <CarouselNext className="relative translate-y-0 right-0" />
            </div>
          </Carousel>
        </motion.div>
      </div>
    </section>
  );
}

function TestimonialCard({ testimonial, index }: { testimonial: Testimonial; index: number }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white p-6 rounded-xl shadow-md border border-[#8b5d53]/10 h-full flex flex-col"
    >
      <div className="mb-4 text-[#8b5d53]">
        <Quote className="w-8 h-8 transform rotate-180" />
      </div>

      <p className="text-stone-600 italic mb-6 flex-grow">{testimonial.content}</p>

      <div className="flex items-center mt-auto">
        <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-[#8b5d53]/20">
          <Image
            src={testimonial.image}
            alt={testimonial.name}
            fill
            className="object-cover"
          />
        </div>
        <div>
          <h3 className="font-medium text-[#8b5d53]">{testimonial.name}</h3>
          {testimonial.role && (
            <p className="text-stone-500 text-sm">{testimonial.role}</p>
          )}
        </div>
      </div>

      {testimonial.rating && (
        <div className="mt-4 flex">
          {Array.from({ length: 5 }).map((_, i) => (
            <svg
              key={i}
              className={cn(
                "w-4 h-4 mr-1",
                i < testimonial.rating! ? "text-[#8b5d53] fill-[#8b5d53]" : "text-stone-300 fill-stone-300"
              )}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
          ))}
        </div>
      )}
    </motion.div>
  );
}

export default TestimonialCarousel;