"use client";

import React from "react";
import { Clock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface AdminHeaderProps {
  title: string;
  onMenuClick?: () => void;
}

export function AdminHeader({ title, onMenuClick }: AdminHeaderProps) {
  const currentDate = new Date().toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
  
  return (
    <header className="sticky top-0 z-10 bg-white border-b border-stone-200 shadow-sm">
      <div className="px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button 
            className="md:hidden p-2 mr-2 text-stone-700 hover:bg-stone-100 rounded-md"
            onClick={onMenuClick}
            aria-label="Toggle menu"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              strokeWidth={1.5} 
              stroke="currentColor" 
              className="w-6 h-6"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
          <h1 className="text-xl font-semibold text-stone-800">{title}</h1>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm" className="hidden sm:flex items-center">
            <Clock className="mr-1 h-4 w-4" />
            <span>{currentDate}</span>
          </Button>
          
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="absolute top-0 right-0 h-2 w-2 bg-amber-500 rounded-full"></div>
              <button className="p-2 rounded-full text-stone-700 hover:bg-stone-100">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  strokeWidth={1.5} 
                  stroke="currentColor" 
                  className="w-5 h-5"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                </svg>
              </button>
            </div>
            
            <div className="flex items-center">
              <button className="flex items-center text-sm font-medium text-stone-700">
                <div className="h-8 w-8 rounded-full bg-amber-200 flex items-center justify-center text-amber-800 font-semibold mr-2">
                  BT
                </div>
                <span className="hidden md:inline">Bryanna Taylor</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 