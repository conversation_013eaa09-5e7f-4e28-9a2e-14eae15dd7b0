"use client";

import React from "react";
import { Star } from "lucide-react";

interface ServiceData {
  name: string;
  percentage: number;
  color: string;
}

interface TopServicesProps {
  services: ServiceData[];
  className?: string;
}

export function TopServices({ services, className = "" }: TopServicesProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-stone-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-stone-800 mb-4">Top Services</h3>
      <div className="space-y-4">
        {services.map((service, index) => (
          <div key={index} className="flex items-center">
            <div className={`w-12 h-12 bg-${service.color}-100 rounded-md flex items-center justify-center text-${service.color}-600 mr-4`}>
              <Star className="h-6 w-6" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-stone-800">{service.name}</h4>
              <div className="flex items-center mt-1">
                <div className="flex-1 bg-stone-200 rounded-full h-2">
                  <div 
                    className={`bg-${service.color}-500 h-2 rounded-full`} 
                    style={{ width: `${service.percentage}%` }}
                  ></div>
                </div>
                <span className="ml-2 text-xs text-stone-500">{service.percentage}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 