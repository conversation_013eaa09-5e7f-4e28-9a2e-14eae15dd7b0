import { NextResponse } from 'next/server';
import { catalogApi } from '../../config';
import { CatalogObject } from 'square';
import { serializeBigInt } from '@/lib/utils';

export async function GET() {
  try {
    // Fetch services from Square Catalog API
    const response = await catalogApi.searchCatalogItems({
      productTypes: ["APPOINTMENTS_SERVICE"],
      limit: 100
    });

    // Get raw service data for debugging
    const rawServices = response.result.items?.map((item: CatalogObject) => {
      const itemData = item.itemData;
      const variations = itemData?.variations?.map(variation => {
        const variationData = variation?.itemVariationData;
        const serviceDuration = variationData?.serviceDuration ? String(variationData.serviceDuration) : undefined;
        
        return {
          id: variation?.id,
          name: variationData?.name,
          serviceDuration: serviceDuration,
          rawDuration: serviceDuration,
          convertedDuration: convertDuration(serviceDuration),
          price: variationData?.priceMoney?.amount
        };
      });

      return {
        id: item.id,
        name: itemData?.name,
        description: itemData?.description,
        variations
      };
    }) || [];

    // Use the utility function to safely serialize the data
    const safeRawServices = serializeBigInt(rawServices);

    return NextResponse.json({
      status: 'success',
      rawServices: safeRawServices
    });

  } catch (error) {
    console.error('Square API Debug Error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to fetch raw service data',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to convert and format duration
function convertDuration(duration: string | undefined): string {
  if (!duration) return 'Not set';
  
  const durationValue = Number(duration);
  
  // Handle seconds vs minutes
  let durationMinutes = durationValue;
  if (durationValue >= 10000) {
    durationMinutes = Math.round(durationValue / 60);
  }
  
  // Format to hours and minutes
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;
  
  let formattedDuration = '';
  if (hours > 0) {
    formattedDuration += `${hours} hour${hours > 1 ? 's' : ''}`;
  }
  if (minutes > 0) {
    if (formattedDuration) formattedDuration += ' ';
    formattedDuration += `${minutes} min`;
  }
  
  return `${formattedDuration} (from ${durationValue})`;
} 