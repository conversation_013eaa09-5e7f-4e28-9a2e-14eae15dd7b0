"use client"

import React, { useEffect, useState, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Home as HomeIcon, List as ServicesIcon, Phone as ContactIcon, Clock } from "lucide-react"
import { cn } from "@/lib/utils"

interface NavItem {
  name: string
  url: string
  icon: string
}

// Icon mapping function
const getIconComponent = (iconName: string) => {
  const iconMap = {
    'Home': HomeIcon,
    'List': ServicesIcon,
    'Phone': ContactIcon,
    'Clock': Clock
  };
  return iconMap[iconName as keyof typeof iconMap] || HomeIcon;
};

interface NavBarProps {
  items: NavItem[]
  className?: string
  children?: React.ReactNode
}

export function NavBar({ items, className, children }: NavBarProps) {
  const pathname = usePathname();

  // Memoize filtered items to prevent unnecessary recalculations
  const memoizedFilteredItems = React.useMemo(() =>
    items.filter(item => item.name !== 'Home'), [items]
  );

  // Calculate initial states based on current pathname - memoized to prevent recalculation
  const initialStates = React.useMemo(() => {
    const isHomePage = pathname === '/' || pathname === '/home';
    if (isHomePage) {
      return { activeTab: '', isLogoActive: true };
    }

    const exactMatch = memoizedFilteredItems.find(item => pathname === item.url);
    if (exactMatch) {
      return { activeTab: exactMatch.name, isLogoActive: false };
    }

    const activeItem = memoizedFilteredItems.find(item =>
      pathname.startsWith(item.url) &&
      item.url !== '/' &&
      item.url.length > 1
    );

    return {
      activeTab: activeItem ? activeItem.name : '',
      isLogoActive: false
    };
  }, [pathname, memoizedFilteredItems]);

  // Initialize states with memoized values
  const [activeTab, setActiveTab] = useState(initialStates.activeTab);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isMobile, setIsMobile] = useState(false)
  const [hasMounted, setHasMounted] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isLogoActive, setIsLogoActive] = useState(initialStates.isLogoActive);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [layoutKey, setLayoutKey] = useState(0); // Add layout key for unique layoutId

  // Improved route detection function with stable dependencies
  const updateActiveTab = useCallback((currentPath: string) => {
    // Prevent updates during transitions
    if (isTransitioning) return;

    // Calculate new states using memoized items
    const isHomePage = currentPath === '/' || currentPath === '/home';
    let newActiveTab = '';
    const newIsLogoActive = isHomePage;

    if (!isHomePage) {
      // First check for exact matches
      const exactMatch = memoizedFilteredItems.find(item => currentPath === item.url);

      if (exactMatch) {
        newActiveTab = exactMatch.name;
      } else {
        // Then check for startsWith matches for non-root paths
        const activeItem = memoizedFilteredItems.find(item =>
          currentPath.startsWith(item.url) &&
          item.url !== '/' &&
          item.url.length > 1
        );
        newActiveTab = activeItem ? activeItem.name : '';
      }
    }

    // Only update if states actually changed
    if (newActiveTab !== activeTab || newIsLogoActive !== isLogoActive) {
      setIsTransitioning(true);

      // Batch state updates to prevent multiple renders
      React.startTransition(() => {
        setIsLogoActive(newIsLogoActive);
        setActiveTab(newActiveTab);
        setLayoutKey(prev => prev + 1); // Update layout key for unique layoutId
      });

      // Clear transition state
      const timeoutId = setTimeout(() => {
        setIsTransitioning(false);
      }, 200);

      return () => clearTimeout(timeoutId);
    }
  }, [memoizedFilteredItems, activeTab, isLogoActive, isTransitioning]);

  // Effect for mounting and initial state setup
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Effect for resize handling (conditionally runs based on hasMounted)
  useEffect(() => {
    if (!hasMounted) return; // Only run on client after mount

    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Add scroll handler
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    handleResize() // Initial check
    window.addEventListener("resize", handleResize)
    window.addEventListener("scroll", handleScroll)

    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("scroll", handleScroll)
    }
  }, [hasMounted])

  // Effect for setting active tab based on path changes only
  useEffect(() => {
    if (!hasMounted || isTransitioning) return;

    // Use a more stable debounce approach
    const timeoutId = setTimeout(() => {
      updateActiveTab(pathname);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [pathname, hasMounted, updateActiveTab, isTransitioning]);

  // Sync states when initial states change (on route change)
  useEffect(() => {
    if (!hasMounted) return;

    // Only update if current states don't match calculated initial states
    if (activeTab !== initialStates.activeTab || isLogoActive !== initialStates.isLogoActive) {
      React.startTransition(() => {
        setActiveTab(initialStates.activeTab);
        setIsLogoActive(initialStates.isLogoActive);
        setLayoutKey(prev => prev + 1);
      });
    }
  }, [initialStates, activeTab, isLogoActive, hasMounted]);

  if (!hasMounted) {
    // Render nothing or a placeholder on the server / before mount
    return null;
  }

  return (
    <motion.div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 flex justify-center",
        className
      )}
      initial={false}
      animate={{
        y: scrolled ? 16 : 24
      }}
      transition={{
        duration: 0.3,
        ease: "easeOut"
      }}
      style={{
        pointerEvents: 'none',
        filter: 'drop-shadow(0 4px 8px rgba(58, 39, 35, 0.2)) drop-shadow(0 1px 3px rgba(176, 124, 112, 0.15))',
        willChange: 'transform'
      }}
    >
      <motion.div
        className={cn(
          "flex items-center gap-2 sm:gap-3 py-1.5 sm:py-1 px-2 sm:px-3 rounded-full shadow-lg relative",
          "w-fit max-w-[95vw] mx-auto", // Ensure proper width constraints and centering
          scrolled
            ? "bg-gradient-to-r from-[#3a2723]/85 via-[#6a4840]/85 to-[#3a2723]/85 border border-[#d4b2a7]/25 backdrop-blur-md"
            : "bg-gradient-to-r from-[#3a2723]/70 via-[#6a4840]/70 to-[#3a2723]/70 border border-[#d4b2a7]/20 backdrop-blur-lg"
        )}
        style={{
          pointerEvents: 'auto',
          minWidth: 'fit-content'
        }}
        transition={{
          duration: 0.3
        }}
      >
        {/* Logo as Home Button */}
        <Link
          href="/home"
          onClick={(e) => {
            if (isTransitioning) {
              e.preventDefault();
              return;
            }
            setIsTransitioning(true);
            React.startTransition(() => {
              setIsLogoActive(true);
              setActiveTab('');
              setLayoutKey(prev => prev + 1);
            });
            setTimeout(() => setIsTransitioning(false), 200);
          }}
          className="relative flex items-center ml-1 sm:ml-2 mr-2 sm:mr-3 cursor-pointer group flex-shrink-0"
        >
          <div className="relative">
            <Image
              src="/glow-by-bry-logo.svg"
              alt="GlowByBry Logo"
              width={120}
              height={48}
              className="object-contain transition-all duration-300 group-hover:scale-105 h-8 sm:h-12 w-auto"
              style={{
                objectFit: 'contain',
                maxWidth: '120px',
                maxHeight: '34px',
                height: 'auto'
              }}
              priority
              unoptimized
              onError={(e) => {
                console.error('Logo failed to load:', e);
                // Fallback to a text logo if image fails
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = document.createElement('div');
                fallback.innerHTML = 'GlowByBry';
                fallback.className = 'text-[#f8f1ee] font-serif text-lg font-medium';
                target.parentNode?.appendChild(fallback);
              }}
            />
            {/* Active state glow for logo - always show a subtle glow, enhanced when active */}
            <div
              className={cn(
                "absolute -inset-1 bg-gradient-to-r from-[#b07c70]/15 via-[#c99a8f]/20 to-[#b07c70]/15 rounded-full blur-sm transition-opacity duration-300",
                isLogoActive ? "opacity-70" : "opacity-30"
              )}
            />
          </div>
        </Link>

        {memoizedFilteredItems.map((item) => {
          const Icon = getIconComponent(item.icon)
          const isActive = activeTab === item.name

          return (
            <Link
              key={item.name}
              href={item.url}
              onClick={(e) => {
                if (isTransitioning) {
                  e.preventDefault();
                  return;
                }
                setIsTransitioning(true);
                React.startTransition(() => {
                  setActiveTab(item.name);
                  setIsLogoActive(false);
                  setLayoutKey(prev => prev + 1);
                });
                setTimeout(() => setIsTransitioning(false), 200);
              }}
              className={cn(
                "relative cursor-pointer text-base font-medium px-3 py-2 sm:px-4 sm:py-2 md:px-6 rounded-full transition-all duration-200 flex-shrink-0",
                "text-[#f0e6e4]/90 hover:text-[#f8f1ee]",
                isActive && "text-[#f8f1ee]"
              )}
            >
              <span className="hidden md:inline relative z-10">{item.name}</span>
              <span className="md:hidden relative z-10 flex items-center justify-center">
                <Icon size={20} strokeWidth={2.5} />
              </span>
              <AnimatePresence mode="wait">
                {isActive && (
                  <motion.div
                    key={`indicator-${item.name}-${layoutKey}`}
                    layoutId={`active-indicator-${layoutKey}`}
                    className="absolute inset-0 w-full bg-gradient-to-r from-[#b07c70]/50 via-[#c99a8f]/60 to-[#b07c70]/50 rounded-full"
                    style={{
                      zIndex: 0,
                    }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{
                      type: "spring",
                      stiffness: 400,
                      damping: 35,
                      mass: 0.6,
                      velocity: 0,
                      duration: 0.3
                    }}
                  >
                  {/* Unique elegant lamp styling with refined glow effect */}
                  <div
                    className="absolute -top-2 left-1/2 w-8 h-1 bg-gradient-to-r from-[#c99a8f] via-[#d4b2a7] to-[#c99a8f] rounded-t-full"
                    style={{
                      marginLeft: '-1rem' // Half of w-8 (2rem) = -1rem for perfect centering
                    }}
                  >
                    <div
                      className="absolute w-12 h-6 bg-[#d4b2a7]/30 rounded-full blur-md -top-2 left-1/2"
                      style={{ marginLeft: '-1.5rem' }} // Half of w-12 (3rem) = -1.5rem
                    />
                    <div
                      className="absolute w-8 h-6 bg-[#c99a8f]/30 rounded-full blur-md -top-1 left-1/2"
                      style={{ marginLeft: '-1rem' }} // Half of w-8 (2rem) = -1rem
                    />
                    <div
                      className="absolute w-4 h-4 bg-[#e8d0c9]/30 rounded-full blur-sm top-0 left-1/2"
                      style={{ marginLeft: '-0.5rem' }} // Half of w-4 (1rem) = -0.5rem
                    />
                  </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </Link>
          )
        })}

        {children && (
          <div className="ml-1">
            {children}
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}

export default NavBar