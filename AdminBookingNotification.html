<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <title>New Booking Notification - Glow by Bry</title>
    <!--[if mso]>
    <style type="text/css">
      table {border-collapse: collapse; border-spacing: 0; margin: 0;}
      div, td {padding: 0;}
      div {margin: 0 !important;}
    </style>
    <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
    </noscript>
    <![endif]-->
    <style type="text/css">
        /* BASE STYLES */
        body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            -webkit-text-size-adjust: 100% !important;
            -ms-text-size-adjust: 100% !important;
            -webkit-font-smoothing: antialiased !important;
        }
        img {
            border: 0 !important;
            outline: none !important;
            display: block !important;
            height: auto !important;
        }
        table, td {
            border-collapse: collapse !important;
        }
        body, #bodyTable {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            background-color: #f2f2f2;
        }
        
        /* DARK MODE STYLES */
        @media (prefers-color-scheme: dark) {
            body, #bodyTable {
                background-color: #1c1c1e !important;
            }
            .darkmode-bg {
                background-color: #2c2c2e !important;
            }
            .darkmode-text {
                color: #f2f2f7 !important;
            }
            .darkmode-subtext {
                color: #d1d1d6 !important;
            }
            .darkmode-header {
                background-color: #8b5d53 !important;
            }
            .darkmode-button {
                background-color: #b07c70 !important;
                border-color: #b07c70 !important;
            }
            .darkmode-section {
                background-color: #3a3a3c !important;
            }
            .darkmode-border {
                border-color: #444444 !important;
            }
            .darkmode-reference {
                background-color: #444444 !important;
            }
            .darkmode-highlight {
                color: #ff9580 !important;
            }
        }
        
        /* RESPONSIVE STYLES */
        @media only screen and (max-width: 600px) {
            .responsive-table {
                width: 100% !important;
            }
            .mobile-padding {
                padding-left: 10px !important;
                padding-right: 10px !important;
            }
            .mobile-stack {
                display: block !important;
                width: 100% !important;
            }
            .mobile-center {
                text-align: center !important;
            }
            .mobile-logo {
                max-width: 180px !important;
                width: 180px !important;
            }
            .hide-mobile {
                display: none !important;
            }
        }
    </style>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f2f2f2;">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" id="bodyTable" style="margin: 0; padding: 0; background-color: #f2f2f2;">
        <tr>
            <td align="center" valign="top" style="padding: 20px 0;">
                <!-- Main Container -->
                <table border="0" cellpadding="0" cellspacing="0" width="600" class="responsive-table" style="max-width: 600px; border-spacing: 0; border-collapse: collapse;">
                    <!-- Logo Header -->
                    <tr>
                        <td align="center" bgcolor="#f0e6e4" style="padding: 20px 0; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                            <img src="https://res.cloudinary.com/streamvista/image/upload/v1747274238/bryanna-logo_om6vxx.png" alt="Glow by Bry Logo" width="200" class="mobile-logo" style="max-width: 200px; display: block; border: 0; height: auto;">
                        </td>
                    </tr>
                    
                    <!-- Header with Alert Icon -->
                    <tr>
                        <td bgcolor="#b07c70" class="darkmode-header" style="padding: 20px; text-align: center; color: white; border: none;">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td align="center" style="padding-bottom: 10px;">
                                        <span style="font-size: 24px; font-weight: bold;">🔔 NEW BOOKING ALERT</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="font-size: 16px;">
                                        A new booking has been made on your website
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Main Content Area -->
                    <tr>
                        <td bgcolor="#ffffff" style="padding: 0; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                            <!-- Booking Details Section -->
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style="padding: 25px 25px 5px 25px;">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td style="padding-bottom: 15px; border-bottom: 2px solid #f0e6e4;">
                                                    <span style="font-size: 18px; font-weight: bold; color: #8b5d53;">BOOKING DETAILS</span>
                                                </td>
                                            </tr>
                                            
                                            <!-- Reference -->
                                            <tr>
                                                <td style="padding-top: 15px; padding-bottom: 10px;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Reference:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px;">
                                                                <span style="display: inline-block; background-color: #f0e6e4; padding: 5px 10px; border-radius: 4px; font-family: monospace; font-weight: bold; color: #8b5d53;" class="darkmode-reference darkmode-text">{{params.reference}}</span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            
                                            <!-- Service -->
                                            <tr>
                                                <td style="padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Service:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px; font-weight: bold; color: #333333;" class="darkmode-text">
                                                                {{params.service}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            
                                            <!-- Service Description (conditional) -->
                                            {% if params.serviceDescription %}
                                            <tr>
                                                <td style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Description:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px; color: #333333;" class="darkmode-text">
                                                                {{params.serviceDescription}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            {% endif %}
                                            
                                            <!-- Date & Time -->
                                            <tr>
                                                <td style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Date & Time:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px; color: #333333;" class="darkmode-text">
                                                                {{params.formattedDate}} at <span style="font-weight: bold; color: #8b5d53;" class="darkmode-highlight">{{params.time}}</span>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            
                                            <!-- Duration (conditional) -->
                                            {% if params.serviceDuration %}
                                            <tr>
                                                <td style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Duration:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px; color: #333333;" class="darkmode-text">
                                                                {{params.serviceDuration}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Client Information Section -->
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style="padding: 5px 25px 5px 25px;">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td style="padding-bottom: 15px; border-bottom: 2px solid #f0e6e4;">
                                                    <span style="font-size: 18px; font-weight: bold; color: #8b5d53;">CLIENT INFORMATION</span>
                                                </td>
                                            </tr>
                                            
                                            <!-- Client Name -->
                                            <tr>
                                                <td style="padding-top: 15px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Name:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px; color: #333333;" class="darkmode-text">
                                                                {{params.name}}
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            
                                            <!-- Client Email -->
                                            <tr>
                                                <td style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Email:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px;">
                                                                <a href="mailto:{{params.email}}" style="color: #b07c70; text-decoration: none;" class="darkmode-highlight">{{params.email}}</a>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            
                                            <!-- Client Phone (conditional) -->
                                            {% if params.phone %}
                                            <tr>
                                                <td style="padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #eeeeee;">
                                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                        <tr>
                                                            <td width="140" style="vertical-align: top; padding-bottom: 5px; color: #666666; font-weight: bold;">
                                                                Phone:
                                                            </td>
                                                            <td style="vertical-align: top; padding-bottom: 5px;">
                                                                <a href="tel:{{params.phone}}" style="color: #b07c70; text-decoration: none;" class="darkmode-highlight">{{params.phone}}</a>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Payment Details Section -->
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style="padding: 5px 25px 5px 25px;">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td style="padding-bottom: 15px; border-bottom: 2px solid #f0e6e4;">
                                                    <span style="font-size: 18px; font-weight: bold; color: #8b5d53;">PAYMENT DETAILS</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 15px 25px 20px;">
                                        <table width="100%" border="0" cellpadding="5" cellspacing="0" bgcolor="#f8f4f2" style="border-radius: 8px;" class="darkmode-section">
                                            <!-- Booking Fee -->
                                            <tr>
                                                <td width="60%" style="padding: 10px 15px; color: #666666; font-weight: bold;">Booking Fee Paid:</td>
                                                <td width="40%" align="right" style="padding: 10px 15px; color: #333333; font-weight: normal;" class="darkmode-text">${{params.bookingFee}}</td>
                                            </tr>
                                            
                                            <!-- Service Price (conditional) -->
                                            {% if params.servicePrice %}
                                            <tr>
                                                <td width="60%" style="padding: 10px 15px; color: #666666; font-weight: bold;">Service Price:</td>
                                                <td width="40%" align="right" style="padding: 10px 15px; color: #333333; font-weight: normal;" class="darkmode-text">${{params.servicePrice}}</td>
                                            </tr>
                                            
                                            <!-- Balance Due (conditional) -->
                                            <tr style="border-top: 1px dashed #dddddd;" class="darkmode-border">
                                                <td width="60%" style="padding: 10px 15px; color: #666666; font-weight: bold;">Balance Due:</td>
                                                <td width="40%" align="right" style="padding: 10px 15px; color: #333333; font-weight: bold;" class="darkmode-text">${{params.balanceDue}}</td>
                                            </tr>
                                            {% endif %}
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Quick Actions Section -->
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style="padding: 5px 25px 5px 25px;">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td style="padding-bottom: 15px; border-bottom: 2px solid #f0e6e4;">
                                                    <span style="font-size: 18px; font-weight: bold; color: #8b5d53;">QUICK ACTIONS</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center" style="padding: 20px 25px 30px 25px;">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <!-- Email Client Button -->
                                                <td style="border-radius: 4px;" align="center" bgcolor="#b07c70" class="darkmode-button">
                                                    <a href="mailto:{{params.email}}?subject=About%20your%20GlowByBry%20appointment%20{{params.reference}}" target="_blank" style="font-size: 16px; font-family: Helvetica, Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 12px 25px; border: 1px solid #b07c70; display: inline-block; border-radius: 4px; font-weight: bold;">
                                                        ✉️ EMAIL CLIENT
                                                    </a>
                                                </td>
                                                
                                                <!-- Spacer -->
                                                <td style="width: 10px;">&nbsp;</td>
                                                
                                                <!-- Call Client Button (conditional) -->
                                                {% if params.phone %}
                                                <td style="border-radius: 4px;" align="center" bgcolor="#8b5d53" class="darkmode-button">
                                                    <a href="tel:{{params.phone}}" target="_blank" style="font-size: 16px; font-family: Helvetica, Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 12px 25px; border: 1px solid #8b5d53; display: inline-block; border-radius: 4px; font-weight: bold;">
                                                        📞 CALL CLIENT
                                                    </a>
                                                </td>
                                                {% endif %}
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="padding: 20px; text-align: center; font-size: 12px; color: #999999;">
                            This is an automated notification from your booking system.
                            <br>© {{params.currentYear}} Glow by Bry
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html> 