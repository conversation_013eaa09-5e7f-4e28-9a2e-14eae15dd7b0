"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Edit, Trash2 } from "lucide-react";

interface ClientInfo {
  name: string;
  email: string;
  image: string;
}

interface BookingData {
  id: string;
  client: ClientInfo;
  service: string;
  date: string;
  time: string;
  amount: string;
  status: "completed" | "upcoming" | "cancelled";
}

interface RecentBookingsProps {
  bookings: BookingData[];
  className?: string;
}

export function RecentBookings({ bookings, className = "" }: RecentBookingsProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden ${className}`}>
      <div className="p-6 border-b border-stone-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-stone-800">Recent Bookings</h3>
          <Link href="/admin/appointments" className="text-sm text-amber-600 hover:text-amber-700">
            View All
          </Link>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-stone-200">
          <thead className="bg-stone-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Service
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Date & Time
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-stone-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-stone-200">
            {bookings.map((booking) => (
              <tr key={booking.id} className="hover:bg-stone-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                      <Image
                        width={40}
                        height={40}
                        className="rounded-full object-cover"
                        src={booking.client.image}
                        alt={booking.client.name}
                      />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-stone-900">{booking.client.name}</div>
                      <div className="text-sm text-stone-500">{booking.client.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-stone-900">{booking.service}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-stone-900">{booking.date}</div>
                  <div className="text-sm text-stone-500">{booking.time}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  {booking.amount}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${booking.status === 'completed'
                      ? 'bg-emerald-100 text-emerald-800'
                      : booking.status === 'upcoming'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                    {booking.status === 'completed'
                      ? 'Completed'
                      : booking.status === 'upcoming'
                        ? 'Upcoming'
                        : 'Cancelled'
                    }
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    className="text-amber-600 hover:text-amber-800 mr-3"
                    aria-label={`Edit booking for ${booking.client.name}`}
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    className="text-red-600 hover:text-red-800"
                    aria-label={`Delete booking for ${booking.client.name}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}