"use client";

import * as React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { CalendarCheck, ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function QuickBookingCTA() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
    >
      <Card className="border-[#8b5d53]/20 shadow-md bg-gradient-to-br from-[#8b5d53]/5 to-[#d4b2a7]/10 backdrop-blur-sm overflow-hidden">
        <CardContent className="p-0">
          <div className="p-6 md:p-8 text-center">
            <div className="w-16 h-16 bg-[#8b5d53]/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <CalendarCheck className="w-8 h-8 text-[#8b5d53]" />
            </div>

            <h3 className="text-xl md:text-2xl font-light tracking-wide text-[#5d3f39] mb-3 uppercase font-serif">
              Ready to Experience the Glow?
            </h3>

            <p className="text-[#5d3f39]/80 mb-6 max-w-md mx-auto font-light leading-relaxed">
              Book your appointment today and take the first step towards a rejuvenated, glowing you.
            </p>

            <Button
              size="lg"
              className="bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white border border-[#8b5d53]/20 shadow-sm"
              asChild
            >
              <Link href="/booking">
                Book Appointment <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
