# GlowByBry Booking System

This is a free, client-side booking system for the GlowByBry beauty spa website. It allows customers to book appointments and provides a simple management interface for the business owner.

## Features

- Multi-step booking form with service selection, date/time picking, and customer information
- Client-side validation to ensure all required information is provided
- Booking confirmation with unique reference number
- Local storage for saving booking data
- Simple booking management interface
- Email notification system (integration ready)

## How It Works

The booking system is entirely client-side and uses the browser's localStorage to store booking data. This makes it completely free to implement and run, with no need for a backend server or database.

### Booking Process

1. Customers select a service from the available options
2. They choose a date and time for their appointment
3. They enter their contact information
4. They review and confirm their booking
5. A confirmation is displayed with a unique booking reference
6. The booking is stored in localStorage

### Management Interface

The booking management interface is available at `/booking/manage`. It allows the business owner to:

- View all bookings
- Search for bookings by reference, name, email, or phone
- Mark bookings as completed or cancelled
- Restore cancelled bookings

## Email Integration

The system includes an email service utility that can be easily integrated with free email services like:

- [EmailJS](https://www.emailjs.com/) - Free tier includes 200 emails/month
- [Formspree](https://formspree.io/) - Free tier includes 50 submissions/month
- [Web3Forms](https://web3forms.com/) - Free tier includes 250 submissions/month

To enable email notifications:

1. Sign up for one of the free email services mentioned above
2. Update the `sendEmail` function in `src/lib/emailService.ts` with your service credentials
3. Uncomment the implementation code for your chosen service

## Limitations

As this is a free, client-side solution, it has some limitations:

- Booking data is stored in the browser's localStorage, which means:
  - Data is only stored on the device where the booking was made
  - Data can be cleared if the browser cache is cleared
  - There's no synchronization between devices
- No authentication for the management interface
- Limited email notification capabilities (depends on free tier limits)
- No automatic calendar integration

## Future Enhancements

For a more robust solution, consider these future enhancements:

1. Add a simple backend server to store booking data in a database
2. Implement user authentication for the management interface
3. Add calendar integration (Google Calendar, iCal, etc.)
4. Implement SMS notifications
5. Add payment processing for deposits or full payments

## Support

For questions or issues, please contact the developer at [<EMAIL>](mailto:<EMAIL>).
