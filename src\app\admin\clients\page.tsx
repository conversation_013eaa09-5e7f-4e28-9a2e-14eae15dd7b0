"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import Image from "next/image";
import {
  Calendar,
  Edit,
  Mail,
  MoreHorizontal,
  Phone,
  Search,
  Trash2,
  User,
  UserPlus,
  X
} from "lucide-react";

import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface ClientData {
  id: string;
  name: string;
  email: string;
  phone: string;
  image: string;
  joinDate: string;
  lastVisit: string;
  totalSpent: string;
  visitsCount: number;
  status: "active" | "inactive" | "new";
  notes?: string;
}

export default function ClientsPage() {
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<"name" | "lastVisit" | "totalSpent">("lastVisit");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [activeClient, setActiveClient] = useState<string | null>(null);

  // Sample data for clients
  const clients: ClientData[] = [
    {
      id: "c1",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/women/11.jpg",
      joinDate: "2022-03-15",
      lastVisit: "2023-08-15",
      totalSpent: "$480",
      visitsCount: 6,
      status: "active",
      notes: "Prefers evening appointments. Allergic to lavender.",
    },
    {
      id: "c2",
      name: "Michael Chen",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/men/22.jpg",
      joinDate: "2022-05-20",
      lastVisit: "2023-08-15",
      totalSpent: "$350",
      visitsCount: 4,
      status: "active",
    },
    {
      id: "c3",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/women/33.jpg",
      joinDate: "2022-07-10",
      lastVisit: "2023-08-16",
      totalSpent: "$820",
      visitsCount: 9,
      status: "active",
      notes: "Interested in skincare products. Has membership.",
    },
    {
      id: "c4",
      name: "David Williams",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/men/44.jpg",
      joinDate: "2022-09-05",
      lastVisit: "2023-08-16",
      totalSpent: "$290",
      visitsCount: 3,
      status: "active",
    },
    {
      id: "c5",
      name: "Sophia Martinez",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/women/55.jpg",
      joinDate: "2022-11-15",
      lastVisit: "2023-08-17",
      totalSpent: "$560",
      visitsCount: 5,
      status: "inactive",
      notes: "Has not visited in over 2 months. Send follow-up promotion.",
    },
    {
      id: "c6",
      name: "James Taylor",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/men/66.jpg",
      joinDate: "2023-01-20",
      lastVisit: "2023-08-18",
      totalSpent: "$180",
      visitsCount: 2,
      status: "new",
    },
    {
      id: "c7",
      name: "Olivia Brown",
      email: "<EMAIL>",
      phone: "(*************",
      image: "https://randomuser.me/api/portraits/women/77.jpg",
      joinDate: "2023-08-01",
      lastVisit: "2023-08-01",
      totalSpent: "$95",
      visitsCount: 1,
      status: "new",
      notes: "First-time client. Referred by Emily Rodriguez.",
    },
  ];

  // Memoize clients array to prevent recreation on each render
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedClients = React.useMemo(() => clients, []);

  // Filter and sort clients
  const filteredClients = React.useMemo(() => {
    let filtered = [...memoizedClients];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(client =>
        client.name.toLowerCase().includes(query) ||
        client.email.toLowerCase().includes(query) ||
        client.phone.includes(query)
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(client => client.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      if (sortBy === "name") {
        return sortOrder === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === "lastVisit") {
        return sortOrder === "asc"
          ? new Date(a.lastVisit).getTime() - new Date(b.lastVisit).getTime()
          : new Date(b.lastVisit).getTime() - new Date(a.lastVisit).getTime();
      } else if (sortBy === "totalSpent") {
        const aAmount = parseFloat(a.totalSpent.replace(/[^0-9.-]+/g, ""));
        const bAmount = parseFloat(b.totalSpent.replace(/[^0-9.-]+/g, ""));
        return sortOrder === "asc" ? aAmount - bAmount : bAmount - aAmount;
      }
      return 0;
    });

    return filtered;
  }, [memoizedClients, searchQuery, statusFilter, sortBy, sortOrder]);

  const toggleSort = (field: "name" | "lastVisit" | "totalSpent") => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc"); // Default to descending when changing sort field
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter(null);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="flex min-h-screen bg-stone-50">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader
          title="Clients"
          onMenuClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <main className="flex-1 overflow-y-auto p-6">
          {/* Page header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-stone-800">Clients</h1>
              <p className="text-sm text-stone-500 mt-1">Manage your client database</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button className="bg-amber-500 hover:bg-amber-600">
                <UserPlus className="mr-2 h-4 w-4" /> Add New Client
              </Button>
            </div>
          </div>

          {/* Filters and search */}
          <div className="mb-6 bg-white p-4 rounded-lg shadow-sm border border-stone-200">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-stone-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search clients..."
                  className="w-full pl-10 pr-4 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-stone-400 hover:text-stone-600"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>

              <div className="flex items-center gap-2">
                <select
                  className="px-4 py-2 bg-white border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  value={statusFilter || ""}
                  onChange={(e) => setStatusFilter(e.target.value || null)}
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="new">New</option>
                </select>

                {(statusFilter || searchQuery) && (
                  <button
                    className="flex items-center gap-1 px-3 py-2 text-sm text-stone-600 hover:text-amber-600 transition-colors"
                    onClick={clearFilters}
                  >
                    <X className="h-3.5 w-3.5" /> Clear
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Client list */}
          <div className="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden">
            <div className="p-6 border-b border-stone-200">
              <h2 className="text-lg font-semibold text-stone-800">All Clients</h2>
              <p className="text-sm text-stone-500">Showing {filteredClients.length} of {clients.length} total clients</p>
            </div>

            {filteredClients.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-stone-200">
                  <thead className="bg-stone-50">
                    <tr>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider cursor-pointer"
                        onClick={() => toggleSort("name")}
                      >
                        <div className="flex items-center">
                          Client
                          {sortBy === "name" && (
                            <span className="ml-1">
                              {sortOrder === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider cursor-pointer"
                        onClick={() => toggleSort("lastVisit")}
                      >
                        <div className="flex items-center">
                          Last Visit
                          {sortBy === "lastVisit" && (
                            <span className="ml-1">
                              {sortOrder === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider cursor-pointer"
                        onClick={() => toggleSort("totalSpent")}
                      >
                        <div className="flex items-center">
                          Total Spent
                          {sortBy === "totalSpent" && (
                            <span className="ml-1">
                              {sortOrder === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-stone-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-stone-200">
                    {filteredClients.map((client) => (
                      <tr
                        key={client.id}
                        className={`hover:bg-stone-50 transition-colors ${
                          activeClient === client.id ? 'bg-stone-50' : ''
                        }`}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0">
                              <Image
                                width={40}
                                height={40}
                                className="rounded-full object-cover"
                                src={client.image}
                                alt={client.name}
                              />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-stone-900">{client.name}</div>
                              <div className="text-xs text-stone-500">Client since {formatDate(client.joinDate)}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-stone-900 flex items-center">
                            <Mail className="h-4 w-4 mr-1.5 text-stone-400" />
                            {client.email}
                          </div>
                          <div className="text-sm text-stone-500 flex items-center">
                            <Phone className="h-4 w-4 mr-1.5 text-stone-400" />
                            {client.phone}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-stone-900">{formatDate(client.lastVisit)}</div>
                          <div className="text-xs text-stone-500">{client.visitsCount} visits total</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-stone-900">{client.totalSpent}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge
                            className={`
                              ${client.status === 'active' ? 'bg-emerald-100 text-emerald-800' : ''}
                              ${client.status === 'inactive' ? 'bg-stone-100 text-stone-800' : ''}
                              ${client.status === 'new' ? 'bg-blue-100 text-blue-800' : ''}
                            `}
                          >
                            {client.status.charAt(0).toUpperCase() + client.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-amber-600 border-amber-200 hover:bg-amber-50"
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              Book
                            </Button>

                            <div className="relative">
                              <button
                                className="p-1 rounded-full text-stone-400 hover:text-stone-600 hover:bg-stone-100"
                                onClick={() => setActiveClient(activeClient === client.id ? null : client.id)}
                                aria-label="Client actions"
                              >
                                <MoreHorizontal className="h-5 w-5" />
                              </button>

                              {activeClient === client.id && (
                                <div className="absolute right-0 top-8 z-10 bg-white shadow-md rounded-md border border-stone-200 py-1 w-32">
                                  <button
                                    className="flex items-center w-full px-3 py-2 text-sm text-stone-700 hover:bg-stone-100"
                                  >
                                    <Edit className="h-4 w-4 mr-2" /> Edit
                                  </button>
                                  <button
                                    className="flex items-center w-full px-3 py-2 text-sm text-stone-700 hover:bg-stone-100"
                                  >
                                    <User className="h-4 w-4 mr-2" /> View
                                  </button>
                                  <button
                                    className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-stone-100"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" /> Delete
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-8 text-center text-stone-500">
                <User className="mx-auto h-12 w-12 text-stone-300 mb-3" />
                <h3 className="text-lg font-medium text-stone-800 mb-1">No clients found</h3>
                <p className="mb-4">Try adjusting your search or filters</p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={clearFilters}
                >
                  Reset Filters
                </Button>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}