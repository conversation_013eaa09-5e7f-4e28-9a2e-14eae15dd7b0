"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import {
  Calendar,
  CreditCard,
  DollarSign,
  FileSpreadsheet,
  PlusCircle,
  Star,
  User,
  Users
} from "lucide-react";

// import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";

// Admin Dashboard Components
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";
import { StatCard } from "@/components/admin/stat-card";
import { RecentBookings } from "@/components/admin/recent-bookings";
import { RevenueChart } from "@/components/admin/revenue-chart";
import { TopServices } from "@/components/admin/top-services";
import { UpcomingAppointments } from "@/components/admin/upcoming-appointments";

export default function AdminDashboardPage() {
  const [isMounted, setIsMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  // Sample data for dashboard
  const stats = [
    { label: "Total Bookings", value: "124", icon: Calendar, change: "+12%", changeType: "positive" as const },
    { label: "Revenue", value: "$6,840", icon: DollarSign, change: "+18%", changeType: "positive" as const },
    { label: "Active Clients", value: "87", icon: Users, change: "+5%", changeType: "positive" as const },
    { label: "Customer Rating", value: "4.8", icon: Star, change: "+0.2", changeType: "positive" as const },
  ];

  const recentBookings = [
    {
      id: "B001",
      client: { name: "Sarah Johnson", email: "<EMAIL>", image: "https://randomuser.me/api/portraits/women/11.jpg" },
      service: "Hydrating Facial",
      date: "2023-08-15",
      time: "10:00 AM",
      amount: "$89",
      status: "completed" as const,
    },
    {
      id: "B002",
      client: { name: "Michael Chen", email: "<EMAIL>", image: "https://randomuser.me/api/portraits/men/22.jpg" },
      service: "Deep Tissue Massage",
      date: "2023-08-15",
      time: "2:30 PM",
      amount: "$99",
      status: "completed" as const,
    },
    {
      id: "B003",
      client: { name: "Emily Rodriguez", email: "<EMAIL>", image: "https://randomuser.me/api/portraits/women/33.jpg" },
      service: "Ultimate Glow Package",
      date: "2023-08-16",
      time: "11:00 AM",
      amount: "$179",
      status: "upcoming" as const,
    },
    {
      id: "B004",
      client: { name: "David Williams", email: "<EMAIL>", image: "https://randomuser.me/api/portraits/men/44.jpg" },
      service: "Anti-Aging Facial",
      date: "2023-08-16",
      time: "3:00 PM",
      amount: "$109",
      status: "upcoming" as const,
    },
    {
      id: "B005",
      client: { name: "Sophia Martinez", email: "<EMAIL>", image: "https://randomuser.me/api/portraits/women/55.jpg" },
      service: "Hot Stone Massage",
      date: "2023-08-17",
      time: "1:00 PM",
      amount: "$119",
      status: "upcoming" as const,
    },
  ];

  const topServices = [
    { name: "Hydrating Facial", percentage: 85, color: "amber" },
    { name: "Deep Tissue Massage", percentage: 70, color: "blue" },
    { name: "Ultimate Glow Package", percentage: 65, color: "emerald" },
  ];

  const upcomingAppointments = [
    {
      id: "A001",
      client: "Emily Rodriguez",
      service: "Ultimate Glow Package",
      date: { month: "AUG", day: 16 },
      time: "11:00 AM",
      duration: "2.5 hrs",
    },
    {
      id: "A002",
      client: "David Williams",
      service: "Anti-Aging Facial",
      date: { month: "AUG", day: 16 },
      time: "3:00 PM",
      duration: "60 min",
    },
    {
      id: "A003",
      client: "Sophia Martinez",
      service: "Hot Stone Massage",
      date: { month: "AUG", day: 17 },
      time: "1:00 PM",
      duration: "75 min",
    },
  ];

  return (
    <div className="flex min-h-screen bg-stone-50">
      {/* Admin Sidebar */}
      <AdminSidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Admin Header */}
        <AdminHeader
          title="Dashboard"
          onMenuClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto p-6">
          {/* Welcome Banner */}
          <div className="mb-6 bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg shadow-md">
            <div className="px-6 py-5 sm:px-8 sm:py-7">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                <div>
                  <h2 className="text-white text-xl sm:text-2xl font-bold">Welcome Back, Bryanna!</h2>
                  <p className="text-amber-100 mt-1">Here&apos;s what&apos;s happening with your business today.</p>
                </div>
                <div className="mt-4 sm:mt-0">
                  <Button className="bg-white text-amber-600 hover:bg-white/90">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add New Appointment
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {stats.map((stat, index) => (
              <StatCard
                key={stat.label}
                label={stat.label}
                value={stat.value}
                icon={stat.icon}
                change={stat.change}
                changeType={stat.changeType}
                iconColor={
                  index === 0 ? "text-blue-600" :
                  index === 1 ? "text-amber-600" :
                  index === 2 ? "text-emerald-600" :
                  "text-purple-600"
                }
                iconBgColor={
                  index === 0 ? "bg-blue-100" :
                  index === 1 ? "bg-amber-100" :
                  index === 2 ? "bg-emerald-100" :
                  "bg-purple-100"
                }
              />
            ))}
          </div>

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Recent Bookings */}
              <RecentBookings bookings={recentBookings} />

              {/* Revenue Chart */}
              <RevenueChart />
            </div>

            {/* Side Column */}
            <div className="space-y-6">
              {/* Top Services */}
              <TopServices services={topServices} />

              {/* Upcoming Appointments */}
              <UpcomingAppointments appointments={upcomingAppointments} />

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
                <h3 className="text-lg font-semibold text-stone-800 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <button className="p-3 bg-stone-50 rounded-lg text-center hover:bg-stone-100">
                    <Calendar className="h-6 w-6 mx-auto text-amber-600 mb-1" />
                    <span className="text-xs font-medium text-stone-800">New Booking</span>
                  </button>
                  <button className="p-3 bg-stone-50 rounded-lg text-center hover:bg-stone-100">
                    <User className="h-6 w-6 mx-auto text-amber-600 mb-1" />
                    <span className="text-xs font-medium text-stone-800">Add Client</span>
                  </button>
                  <button className="p-3 bg-stone-50 rounded-lg text-center hover:bg-stone-100">
                    <FileSpreadsheet className="h-6 w-6 mx-auto text-amber-600 mb-1" />
                    <span className="text-xs font-medium text-stone-800">Reports</span>
                  </button>
                  <button className="p-3 bg-stone-50 rounded-lg text-center hover:bg-stone-100">
                    <CreditCard className="h-6 w-6 mx-auto text-amber-600 mb-1" />
                    <span className="text-xs font-medium text-stone-800">Payments</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}