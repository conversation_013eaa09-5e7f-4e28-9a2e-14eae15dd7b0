import { NextResponse } from 'next/server';
import { sendBookingConfirmation } from '@/lib/emailService';

export async function POST(req: Request) {
  try {
    const data = await req.json();
    
    // Send the email using the server-side email service
    const emailResult = await sendBookingConfirmation({
      reference: data.reference,
      service: data.serviceName,
      date: data.date,
      time: data.time,
      name: data.name,
      email: data.email,
      phone: data.phone,
      signature: data.signature,
      signatureDate: data.signatureDate,
      consentAgreed: data.consentAgreed,
      bookingFee: data.bookingFee,
      serviceDetails: {
        price: data.servicePrice,
        duration: data.serviceDuration,
        description: data.serviceDescription
      }
    });

    return NextResponse.json(emailResult);
  } catch (error) {
    console.error('Error in email API route:', error);
    return NextResponse.json(
      { success: false, message: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
} 