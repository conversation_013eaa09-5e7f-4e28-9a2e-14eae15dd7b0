import { Client, Environment } from 'square';

if (!process.env.SQUARE_ACCESS_TOKEN) {
  throw new Error('Square access token is not defined');
}

if (!process.env.SQUARE_LOCATION_ID) {
  throw new Error('Square location ID is not defined');
}

console.log('Initializing Square client with:', {
  environment: 'Production', // Always use production
  locationId: process.env.SQUARE_LOCATION_ID,
  hasAccessToken: !!process.env.SQUARE_ACCESS_TOKEN
});

// Configure Square client
const squareClient = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN || '',
  environment: Environment.Production, // Force production environment
  userAgentDetail: 'GlowByBry' // Application name
});

// Export specific API instances
export const bookingsApi = squareClient.bookingsApi;
export const catalogApi = squareClient.catalogApi;
export const customersApi = squareClient.customersApi;
export const locationsApi = squareClient.locationsApi; 