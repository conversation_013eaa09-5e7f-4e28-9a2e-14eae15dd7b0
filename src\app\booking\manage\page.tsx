"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { BeamsBackground } from "@/components/ui/beams-background";

import { formatDate as formatDateUtil } from "@/lib/dateUtils";
import {
  Check,
  X,
  Search,
  RefreshCw
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";



// Service options mapping for display
const serviceMap: { [key: string]: string } = {
  "facial-1": "Customized Facials",
  "facial-2": "Collagen Induction",
  "facial-3": "A La Carte",
  "massage-1": "Lash Lift",
  "massage-2": "Waxing Services",
  "package-1": "The Glow Edit"
};

interface Booking {
  reference: string;
  service: string;
  date: string;
  time: string;
  name: string;
  email: string;
  phone: string;
  notes?: string;
  createdAt: string;
  status: 'confirmed' | 'cancelled' | 'completed';
}

export default function ManageBookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [filteredBookings, setFilteredBookings] = useState<Booking[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Load bookings from localStorage on component mount
  useEffect(() => {
    const loadBookings = () => {
      try {
        const storedBookings = localStorage.getItem('bookings');
        if (storedBookings) {
          const parsedBookings = JSON.parse(storedBookings) as Booking[];
          setBookings(parsedBookings);
          setFilteredBookings(parsedBookings);
        }
      } catch (error) {
        console.error('Error loading bookings:', error);
      }
    };

    loadBookings();
  }, []);

  // Filter bookings based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredBookings(bookings);
      return;
    }

    const term = searchTerm.toLowerCase();
    const filtered = bookings.filter(booking =>
      booking.reference.toLowerCase().includes(term) ||
      booking.name.toLowerCase().includes(term) ||
      booking.email.toLowerCase().includes(term) ||
      booking.phone.toLowerCase().includes(term)
    );

    setFilteredBookings(filtered);
  }, [searchTerm, bookings]);

  // Handle booking status change
  const handleStatusChange = (reference: string, newStatus: 'confirmed' | 'cancelled' | 'completed') => {
    const updatedBookings = bookings.map(booking =>
      booking.reference === reference
        ? { ...booking, status: newStatus }
        : booking
    );

    setBookings(updatedBookings);
    setFilteredBookings(
      filteredBookings.map(booking =>
        booking.reference === reference
          ? { ...booking, status: newStatus }
          : booking
      )
    );

    // Update localStorage
    localStorage.setItem('bookings', JSON.stringify(updatedBookings));
  };

  // Use the formatDate utility from dateUtils

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <BeamsBackground />
      <div className="container mx-auto px-4 py-16 min-h-screen">
        <div className="max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-10"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-stone-800 mb-4">Manage Bookings</h1>
            <p className="text-stone-600">
              View and manage your appointment bookings.
            </p>
          </motion.div>

          <Card className="border-stone-200 shadow-md bg-white/80 backdrop-blur-sm mb-8">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl">Bookings</CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-stone-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search bookings..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-stone-500 w-64"
                  />
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {filteredBookings.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-stone-200">
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Reference</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Customer</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Service</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Date & Time</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Status</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-stone-500">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredBookings.map((booking) => (
                        <tr key={booking.reference} className="border-b border-stone-100 hover:bg-stone-50">
                          <td className="px-4 py-4 text-sm font-mono font-medium text-stone-800">
                            {booking.reference}
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm font-medium text-stone-800">{booking.name}</div>
                            <div className="text-xs text-stone-500">{booking.email}</div>
                            <div className="text-xs text-stone-500">{booking.phone}</div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm text-stone-800">
                              {serviceMap[booking.service as keyof typeof serviceMap] || booking.service}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm text-stone-800">{formatDateUtil(booking.date)}</div>
                            <div className="text-xs text-stone-500">{booking.time}</div>
                          </td>
                          <td className="px-4 py-4">
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex space-x-2">
                              {booking.status !== 'completed' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs"
                                  onClick={() => handleStatusChange(booking.reference, 'completed')}
                                >
                                  <Check className="w-3 h-3 mr-1" /> Complete
                                </Button>
                              )}
                              {booking.status !== 'cancelled' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs text-red-600 border-red-200 hover:bg-red-50"
                                  onClick={() => handleStatusChange(booking.reference, 'cancelled')}
                                >
                                  <X className="w-3 h-3 mr-1" /> Cancel
                                </Button>
                              )}
                              {booking.status === 'cancelled' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs text-green-600 border-green-200 hover:bg-green-50"
                                  onClick={() => handleStatusChange(booking.reference, 'confirmed')}
                                >
                                  <RefreshCw className="w-3 h-3 mr-1" /> Restore
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-stone-500">No bookings found.</p>
                  {searchTerm && (
                    <p className="text-stone-400 text-sm mt-2">
                      Try adjusting your search or clear the search field to see all bookings.
                    </p>
                  )}
                </div>
              )}
            </CardContent>

            <CardFooter className="flex justify-between">
              <div className="text-sm text-stone-500">
                {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''} found
              </div>
              <Button
                variant="outline"
                onClick={() => setSearchTerm('')}
                disabled={!searchTerm}
              >
                Clear Search
              </Button>
            </CardFooter>
          </Card>

          <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 text-sm text-amber-800">
            <p className="font-medium mb-2">About This Booking System</p>
            <p>
              This is a simple booking management system that stores appointments locally in your browser.
              In a production environment, you would want to:
            </p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Connect to a database to store bookings securely</li>
              <li>Implement user authentication for the management page</li>
              <li>Set up email notifications for new bookings and status changes</li>
              <li>Add calendar integration for better scheduling</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
}
