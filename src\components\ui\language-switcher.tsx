"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { useLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
          <Globe className="h-5 w-5" />
          <span className="sr-only">Toggle language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-white/90 backdrop-blur-sm border-[#8b5d53]/20">
        <DropdownMenuItem 
          onClick={() => setLanguage('en')}
          className={`flex items-center gap-2 ${language === 'en' ? 'bg-[#8b5d53]/10 text-[#5d3f39] font-medium' : ''}`}
        >
          <span className="text-base">🇺🇸</span> English
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => setLanguage('es')}
          className={`flex items-center gap-2 ${language === 'es' ? 'bg-[#8b5d53]/10 text-[#5d3f39] font-medium' : ''}`}
        >
          <span className="text-base">🇲🇽</span> Español
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
