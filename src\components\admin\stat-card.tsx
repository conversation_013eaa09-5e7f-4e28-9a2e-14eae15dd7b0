"use client";

import React from "react";
import { LucideIcon } from "lucide-react";

interface StatCardProps {
  label: string;
  value: string;
  icon: LucideIcon;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  className?: string;
  iconColor?: string;
  iconBgColor?: string;
}

export function StatCard({
  label,
  value,
  icon: Icon,
  change,
  changeType,
  className = "",
  iconColor = "text-amber-600",
  iconBgColor = "bg-amber-100",
}: StatCardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-stone-200 p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-stone-500">{label}</p>
          <h3 className="mt-1 text-2xl font-semibold text-stone-800">{value}</h3>
        </div>
        <div className={`p-3 rounded-full ${iconBgColor} ${iconColor}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      <p 
        className={`mt-2 text-sm ${
          changeType === "positive" 
            ? "text-emerald-600" 
            : changeType === "negative" 
              ? "text-red-600" 
              : "text-stone-500"
        }`}
      >
        {change} {
          changeType === "positive" 
            ? "increase" 
            : changeType === "negative" 
              ? "decrease" 
              : "change"
        } from last month
      </p>
    </div>
  );
} 