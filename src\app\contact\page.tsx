"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { BeamsBackground } from "@/components/ui/beams-background";
import { SparklesCore } from "@/components/ui/sparkles";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  MapPin,
  Mail,
  Clock,
  Send,
  CheckCircle2,
  PhoneCall as Phone
} from "lucide-react";



export default function ContactPage() {
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Check for success query param on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('success') === 'true') {
      setIsSubmitted(true);
    }
  }, []);

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    message: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    // The form will be reset and success message shown after redirect
  };

  return (
    <>
      <BeamsBackground />

      {/* Add sparkles effect */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <SparklesCore
          background="transparent"
          minSize={0.6}
          maxSize={1.4}
          particleDensity={60}
          className="w-full h-full"
          particleColor="#8b5d53"
        />
      </div>

      {/* Add floating gradient orbs for additional effect */}
      <div className="fixed top-1/4 left-10 w-64 h-64 rounded-full bg-[#b07c70]/15 blur-3xl animate-float pointer-events-none"></div>
      <div className="fixed bottom-1/4 right-10 w-80 h-80 rounded-full bg-[#8b5d53]/10 blur-3xl animate-float-delay pointer-events-none"></div>
      <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-[#d4b2a7]/10 blur-3xl animate-pulse-slow pointer-events-none"></div>

      <div className="container mx-auto px-4 py-8 pt-24 md:pt-36 md:py-16 min-h-screen">
        <motion.div
          className="max-w-5xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="text-center mb-8 md:mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-light tracking-wide mb-4 md:mb-6 text-[#5d3f39] uppercase font-serif">Contact Us</h1>
            <p className="text-lg text-[#5d3f39]/80 max-w-3xl mx-auto px-0 md:px-4 leading-relaxed font-light">
              Have questions or ready to book your appointment? Reach out to us and we&apos;ll get back to you as soon as possible.
            </p>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-[#8b5d53] to-transparent mx-auto mt-4 md:mt-8"></div>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-2 gap-4 md:gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="border-[#8b5d53]/20 shadow-md bg-white/80 backdrop-blur-sm overflow-hidden">
                <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-2 px-4 md:py-3 md:px-6">
                  <CardTitle className="text-xl text-[#5d3f39] font-light tracking-wide uppercase font-serif">Send Us a Message</CardTitle>
                  <CardDescription className="text-[#5d3f39]/80 font-light text-sm">
                    Fill out the form below and we&apos;ll get back to you shortly.
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-3 md:pt-4 px-4 md:px-6">
                  {!isSubmitted ? (
                    <form onSubmit={handleSubmit} className="space-y-2 md:space-y-3" action="https://formsubmit.co/<EMAIL>" method="POST">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
                        <div className="space-y-1 md:space-y-2">
                          <label className="text-sm font-medium text-stone-700" htmlFor="firstName">
                            First Name
                          </label>
                          <Input
                            id="firstName"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleChange}
                            placeholder="Your first name"
                            className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-0 focus:border-[#8b5d53]/40 bg-white/90 text-base [&:focus-visible]:ring-0 [&:focus-visible]:ring-offset-0 [-webkit-tap-highlight-color:transparent]"
                            required
                          />
                        </div>
                        <div className="space-y-1 md:space-y-2">
                          <label className="text-sm font-medium text-stone-700" htmlFor="lastName">
                            Last Name
                          </label>
                          <Input
                            id="lastName"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleChange}
                            placeholder="Your last name"
                            className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-0 focus:border-[#8b5d53]/40 bg-white/90 text-base [&:focus-visible]:ring-0 [&:focus-visible]:ring-offset-0 [-webkit-tap-highlight-color:transparent]"
                            required
                          />
                        </div>
                      </div>
                      <div className="space-y-1 md:space-y-2">
                        <label className="text-sm font-medium text-stone-700" htmlFor="email">
                          Email Address
                        </label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleChange}
                          placeholder="Your email"
                          className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-0 focus:border-[#8b5d53]/40 bg-white/90 text-base [&:focus-visible]:ring-0 [&:focus-visible]:ring-offset-0 [-webkit-tap-highlight-color:transparent]"
                          required
                        />
                      </div>
                      <div className="space-y-1 md:space-y-2">
                        <label className="text-sm font-medium text-stone-700" htmlFor="phone">
                          Phone Number
                        </label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleChange}
                          placeholder="Your phone number"
                          className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-0 focus:border-[#8b5d53]/40 bg-white/90 text-base [&:focus-visible]:ring-0 [&:focus-visible]:ring-offset-0 [-webkit-tap-highlight-color:transparent]"
                          required
                        />
                      </div>
                      <div className="space-y-1 md:space-y-2">
                        <label className="text-sm font-medium text-stone-700" htmlFor="message">
                          Message
                        </label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          placeholder="How can we help you?"
                          className="w-full p-3 border border-[#8b5d53]/20 rounded-md focus:outline-none focus:ring-0 focus:border-[#8b5d53]/40 bg-white/90 text-base min-h-[120px] resize-y [&:focus-visible]:ring-0 [&:focus-visible]:ring-offset-0 [-webkit-tap-highlight-color:transparent]"
                          rows={3}
                          required
                        />
                      </div>
                      <input type="hidden" name="_next" value="https://glowbybry.com/contact?success=true" />
                      <input type="hidden" name="_subject" value="New Contact Form Submission" />
                      <input type="hidden" name="_captcha" value="false" />
                      <input type="hidden" name="_template" value="table" />
                      <input type="hidden" name="_required" value="firstName,lastName,email,phone" />
                      <Button
                        type="submit"
                        className="w-full bg-[#8b5d53] hover:bg-[#8b5d53]/90 text-white border border-[#8b5d53]/20 shadow-sm"
                      >
                        <span className="flex items-center">
                          <Send className="mr-2 h-4 w-4" />
                          Send Message
                        </span>
                      </Button>
                    </form>
                  ) : (
                    <div className="py-4 md:py-8 text-center">
                      <div className="w-14 h-14 md:w-16 md:h-16 bg-[#8b5d53]/20 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4">
                        <CheckCircle2 className="w-7 h-7 md:w-8 md:h-8 text-[#8b5d53]" />
                      </div>
                      <h3 className="text-lg md:text-xl font-light tracking-wide text-[#5d3f39] mb-2 uppercase font-serif">Message Sent!</h3>
                      <p className="text-[#5d3f39]/80 mb-3 md:mb-4 font-light text-sm md:text-base">
                        Thank you for contacting us. We&apos;ll get back to you as soon as possible.
                      </p>
                      <Button
                        type="button"
                        onClick={() => setIsSubmitted(false)}
                        variant="outline"
                        className="text-[#8b5d53] border-[#8b5d53]/20 hover:bg-[#8b5d53]/10"
                      >
                        Send Another Message
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="space-y-2"
            >
              <Card className="border-[#8b5d53]/20 shadow-md bg-white/80 backdrop-blur-sm overflow-hidden">
                <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-2 px-4 md:py-3 md:px-6">
                  <CardTitle className="text-xl text-[#5d3f39] font-light tracking-wide uppercase font-serif">Visit Us</CardTitle>
                </CardHeader>
                <CardContent className="p-2 md:p-3">
                  <div className="grid grid-cols-1 gap-3.5">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-spa-rosegold flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-[#8b5d53] text-base mb-0.5">Our Location</h3>
                        <p className="text-stone-600 text-base leading-tight">77935 Calle Tampico #103, La Quinta, CA 92253</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-spa-rosegold flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-[#8b5d53] text-base mb-0.5">Email</h3>
                        <p className="text-stone-600 text-base leading-tight"><EMAIL></p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-spa-rosegold flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-[#8b5d53] text-base mb-0.5">Phone</h3>
                        <p className="text-stone-600 text-base leading-tight">(*************</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <Clock className="h-5 w-5 text-spa-rosegold flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-[#8b5d53] text-base mb-0.5">Hours</h3>
                        <div className="text-stone-600 text-base leading-tight space-y-1">
                          <div>Monday - Friday: 10:00 AM - 5:00 PM</div>
                          <div>Saturday: 10:00 AM - 3:00 PM</div>
                          <div>Sunday: Closed</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="mt-0"
              >
                <Card className="border-[#8b5d53]/20 shadow-md bg-white/80 backdrop-blur-sm overflow-hidden">
                  <CardHeader className="bg-[#8b5d53]/10 border-b border-[#8b5d53]/10 py-2 px-4 md:py-4 md:px-6">
                    <CardTitle className="text-xl text-[#5d3f39] font-light tracking-wide uppercase font-serif">Find Us</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="aspect-video w-full bg-stone-100 relative max-h-[180px] md:max-h-none">
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3315.012283492089!2d-116.30046402457166!3d33.68047193759761!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80daf43543d39c67%3A0x6b0bbf51f87edcc4!2s77935%20Calle%20Tampico%20%23103%2C%20La%20Quinta%2C%20CA%2092253!5e0!3m2!1sen!2sus!4v1720225001639!5m2!1sen!2sus"
                        width="100%"
                        height="100%"
                        style={{ border: 0 }}
                        allowFullScreen
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        title="GlowByBry Location"
                        className="absolute inset-0"
                      ></iframe>
                    </div>
                  </CardContent>
                  <div className="p-2 border-t border-[#8b5d53]/10 bg-[#8b5d53]/5">
                    <a
                      href="https://maps.app.goo.gl/K4h5MTZfqgZMpjqt9"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#8b5d53] hover:text-[#8b5d53]/80 text-sm flex justify-center font-medium py-1"
                    >
                      Get Directions
                    </a>
                  </div>
                </Card>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}