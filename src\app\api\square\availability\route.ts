import { NextResponse } from 'next/server';
import { bookingsApi } from '../config';

if (!process.env.SQUARE_STAFF_ID) {
  throw new Error('Square staff ID is not defined');
}

// Define business hours
interface BusinessHours {
  open: boolean;
  start?: string;
  end?: string;
}

const BUSINESS_HOURS: Record<number, BusinessHours> = {
  0: { open: false }, // Sunday (closed)
  1: { open: true, start: "10:00", end: "17:00" }, // Monday
  2: { open: true, start: "10:00", end: "17:00" }, // Tuesday
  3: { open: true, start: "10:00", end: "17:00" }, // Wednesday
  4: { open: true, start: "10:00", end: "17:00" }, // Thursday
  5: { open: true, start: "10:00", end: "17:00" }, // Friday
  6: { open: true, start: "10:00", end: "15:00" }  // Saturday
};

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { serviceId, startDate } = body;

    console.log('Availability request:', { 
      serviceId, 
      startDate,
      staffId: process.env.SQUARE_STAFF_ID 
    });

    // Parse the selected date and ensure it's in the local timezone
    const selectedDate = new Date(startDate);
    
    // Add timezone debugging info
    console.log('Date parsing debug:', {
      inputDate: startDate,
      parsedDate: selectedDate.toString(),
      localeString: selectedDate.toLocaleString(),
      timezoneOffset: selectedDate.getTimezoneOffset()
    });
    
    // Check if the date is valid
    if (isNaN(selectedDate.getTime())) {
      console.error('Invalid date format received:', startDate);
      return NextResponse.json({
        status: 'error',
        message: 'Invalid date format'
      }, { status: 400 });
    }

    const dayOfWeek = selectedDate.getDay(); // 0-6, where 0 is Sunday

    // Check if the business is open on the selected day
    if (!BUSINESS_HOURS[dayOfWeek]?.open) {
      console.log(`Business is closed on ${startDate} (day ${dayOfWeek})`);
      return NextResponse.json({
        status: 'success',
        availableSlots: []
      });
    }

    // Calculate date range - expand to a few days if needed
    // Include current day plus next 2 days for more options
    const startDateTime = new Date(startDate);
    startDateTime.setHours(0, 0, 0, 0); // Start at midnight
    
    const endDateTime = new Date(startDateTime);
    endDateTime.setDate(endDateTime.getDate() + 0); // Just one day for now
    endDateTime.setHours(23, 59, 59, 999); // End of the day

    console.log('Date range:', {
      start: startDateTime.toISOString(),
      end: endDateTime.toISOString()
    });

    // Search for availability
    const { result } = await bookingsApi.searchAvailability({
      query: {
        filter: {
          startAtRange: {
            startAt: startDateTime.toISOString(),
            endAt: endDateTime.toISOString()
          },
          locationId: process.env.SQUARE_LOCATION_ID!,
          segmentFilters: [
            {
              serviceVariationId: serviceId,
              teamMemberIdFilter: {
                any: [process.env.SQUARE_STAFF_ID!]
              }
            }
          ]
        }
      }
    });

    console.log('Availability response from Square:', {
      availabilityCount: result.availabilities?.length || 0
    });
    
    if (result.availabilities && result.availabilities.length > 0) {
      console.log('Sample availability:', result.availabilities[0]);
    }

    if (!result.availabilities || result.availabilities.length === 0) {
      console.log('No availabilities returned from Square API');
      
      // Create an array of fixed business hours time slots in local timezone format
      // Using the startDate but removing the 'Z' indicator to prevent UTC interpretation
      const businessDate = startDate.split('T')[0]; // Extract just the date portion
      
      const fixedBusinessHoursSlots = [
        { startAt: `${businessDate}T10:00:00.000` }, // 10:00 AM local time
        { startAt: `${businessDate}T10:30:00.000` }, // 10:30 AM local time
        { startAt: `${businessDate}T11:00:00.000` }, // 11:00 AM local time
        { startAt: `${businessDate}T11:30:00.000` }, // 11:30 AM local time
        { startAt: `${businessDate}T12:00:00.000` }, // 12:00 PM local time
        { startAt: `${businessDate}T12:30:00.000` }, // 12:30 PM local time
        { startAt: `${businessDate}T13:00:00.000` }, // 1:00 PM local time
        { startAt: `${businessDate}T13:30:00.000` }, // 1:30 PM local time
        { startAt: `${businessDate}T14:00:00.000` }, // 2:00 PM local time
        { startAt: `${businessDate}T14:30:00.000` }, // 2:30 PM local time
        { startAt: `${businessDate}T15:00:00.000` }, // 3:00 PM local time
        { startAt: `${businessDate}T15:30:00.000` }, // 3:30 PM local time
        { startAt: `${businessDate}T16:00:00.000` }, // 4:00 PM local time
        { startAt: `${businessDate}T16:30:00.000` }, // 4:30 PM local time
      ];
      
      console.log('Using fixed time slots for consistency:');
      console.log(fixedBusinessHoursSlots);
      
      return NextResponse.json({
        status: 'success',
        availableSlots: fixedBusinessHoursSlots
      });
    }

    // Get business hours for the selected day
    const businessHours = BUSINESS_HOURS[dayOfWeek];
    
    if (!businessHours.start || !businessHours.end) {
      // This shouldn't happen because we check for open above, but TypeScript requires the check
      return NextResponse.json({
        status: 'success',
        availableSlots: []
      });
    }
    
    const [openHour, openMinute] = businessHours.start.split(":").map(Number);
    const [closeHour, closeMinute] = businessHours.end.split(":").map(Number);
    
    // Filter time slots based on business hours
    const filteredSlots = result.availabilities.filter(slot => {
      // Check if startAt is defined before using it
      if (!slot.startAt) return false;
      
      try {
        const slotDate = new Date(slot.startAt);
        const slotHour = slotDate.getHours();
        const slotMinute = slotDate.getMinutes();
        
        // Convert to minutes for easier comparison
        const slotTimeInMinutes = slotHour * 60 + slotMinute;
        const openTimeInMinutes = openHour * 60 + openMinute;
        const closeTimeInMinutes = closeHour * 60 + closeMinute;
        
        // Keep only slots within business hours
        return slotTimeInMinutes >= openTimeInMinutes && slotTimeInMinutes < closeTimeInMinutes;
      } catch (e) {
        console.error('Error parsing slot date:', e);
        return false;
      }
    });

    console.log(`Filtered slots: ${filteredSlots.length} of ${result.availabilities.length} slots are within business hours`);

    // Transform filtered availabilities into time slots
    const availableSlots = filteredSlots.map(slot => ({
      startAt: slot.startAt!
    }));

    return NextResponse.json({
      status: 'success',
      availableSlots
    });

  } catch (error) {
    console.error('Availability API Error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to get availability',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 