"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import {
  Edit,
  Save,
  Image as ImageIcon
} from "lucide-react";
import Image from "next/image";

import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminHeader } from "@/components/admin/admin-header";
import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

interface BusinessSettings {
  name: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  logo: string;
  workingHours: {
    monday: string;
    tuesday: string;
    wednesday: string;
    thursday: string;
    friday: string;
    saturday: string;
    sunday: string;
  };
}

interface AccountSettings {
  name: string;
  email: string;
  role: string;
  profilePicture: string;
}

interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  appointmentReminders: boolean;
  marketingEmails: boolean;
}

export default function SettingsPage() {
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  // const [activeTab, setActiveTab] = useState("business");
  const [isEditing, setIsEditing] = useState(false);

  // Sample data for settings
  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({
    name: "Glow by Bry",
    email: "<EMAIL>",
    phone: "(*************",
    address: "123 Beauty Lane, Suite 200, Los Angeles, CA 90210",
    website: "www.glowbybry.com",
    logo: "/images/logo.png",
    workingHours: {
      monday: "9:00 AM - 6:00 PM",
      tuesday: "9:00 AM - 6:00 PM",
      wednesday: "9:00 AM - 6:00 PM",
      thursday: "9:00 AM - 8:00 PM",
      friday: "9:00 AM - 8:00 PM",
      saturday: "10:00 AM - 5:00 PM",
      sunday: "Closed"
    }
  });

  const [accountSettings, setAccountSettings] = useState<AccountSettings>({
    name: "Bryanna Smith",
    email: "<EMAIL>",
    role: "Administrator",
    profilePicture: "https://images.unsplash.com/photo-*************-bcfd4ca60f91?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
  });

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: true,
    appointmentReminders: true,
    marketingEmails: false
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const handleSaveBusinessSettings = () => {
    // In a real app, you would save the settings to the backend here
    setIsEditing(false);
    // Show success toast or message
  };

  const handleToggleNotification = (setting: keyof NotificationSettings) => {
    setNotificationSettings({
      ...notificationSettings,
      [setting]: !notificationSettings[setting]
    });
  };

  // Fix for the handleChange function types
  // Removed unused function

  return (
    <div className="flex min-h-screen bg-stone-50">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader
          title="Settings"
          onMenuClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        />

        <main className="flex-1 overflow-y-auto p-6">
          {/* Page header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-stone-800">Settings</h1>
              <p className="text-sm text-stone-500 mt-1">Manage your business and account settings</p>
            </div>
          </div>

          {/* Settings Tabs */}
          <Tabs defaultValue="business" className="w-full">
            <TabsList className="grid grid-cols-3 mb-8">
              <TabsTrigger value="business">Business</TabsTrigger>
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
            </TabsList>

            {/* Business Settings Tab */}
            <TabsContent value="business">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Business Information</CardTitle>
                    <CardDescription>Manage your business details and operating hours</CardDescription>
                  </div>
                  <Button
                    variant={isEditing ? "default" : "outline"}
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? (
                      <>
                        <Save className="mr-2 h-4 w-4" /> Save
                      </>
                    ) : (
                      <>
                        <Edit className="mr-2 h-4 w-4" /> Edit
                      </>
                    )}
                  </Button>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="business-name">Business Name</Label>
                        <Input
                          id="business-name"
                          value={businessSettings.name}
                          disabled={!isEditing}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBusinessSettings({...businessSettings, name: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="business-email">Email</Label>
                        <Input
                          id="business-email"
                          value={businessSettings.email}
                          disabled={!isEditing}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBusinessSettings({...businessSettings, email: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="business-phone">Phone</Label>
                        <Input
                          id="business-phone"
                          value={businessSettings.phone}
                          disabled={!isEditing}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBusinessSettings({...businessSettings, phone: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="business-website">Website</Label>
                        <Input
                          id="business-website"
                          value={businessSettings.website}
                          disabled={!isEditing}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBusinessSettings({...businessSettings, website: e.target.value})}
                        />
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="business-address">Address</Label>
                        <Textarea
                          id="business-address"
                          value={businessSettings.address}
                          disabled={!isEditing}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setBusinessSettings({...businessSettings, address: e.target.value})}
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="business-logo">Logo</Label>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="h-16 w-16 bg-white rounded-md border border-stone-200 flex items-center justify-center overflow-hidden">
                            <Image
                              src={businessSettings.logo}
                              alt="Business Logo"
                              width={100}
                              height={100}
                              className="max-h-full max-w-full object-contain"
                            />
                          </div>
                          {isEditing && (
                            <Button variant="outline" size="sm">
                              <ImageIcon className="mr-2 h-4 w-4" /> Change Logo
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Business Hours</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                      {Object.entries(businessSettings.workingHours).map(([day, hours]) => (
                        <div key={day} className="flex justify-between items-center">
                          <span className="font-medium capitalize">{day}</span>
                          <Input
                            value={hours}
                            className="w-48"
                            disabled={!isEditing}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBusinessSettings({
                              ...businessSettings,
                              workingHours: {
                                ...businessSettings.workingHours,
                                [day]: e.target.value
                              }
                            })}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
                {isEditing && (
                  <CardFooter className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsEditing(false)}>Cancel</Button>
                    <Button onClick={handleSaveBusinessSettings}>Save Changes</Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            {/* Account Settings Tab */}
            <TabsContent value="account">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>Manage your personal account details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex flex-col sm:flex-row gap-6 items-start sm:items-center mb-6">
                    <div className="h-24 w-24 bg-white rounded-full border border-stone-200 flex items-center justify-center overflow-hidden">
                      <Image
                        src={accountSettings.profilePicture}
                        alt="Profile"
                        width={96}
                        height={96}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-medium">{accountSettings.name}</h3>
                      <p className="text-sm text-stone-500">{accountSettings.role}</p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <ImageIcon className="mr-2 h-4 w-4" /> Change Picture
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="account-name">Full Name</Label>
                      <Input
                        id="account-name"
                        value={accountSettings.name}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setAccountSettings({...accountSettings, name: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="account-email">Email</Label>
                      <Input
                        id="account-email"
                        value={accountSettings.email}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setAccountSettings({...accountSettings, email: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="pt-6 border-t border-stone-200">
                    <h3 className="text-lg font-medium mb-4">Password</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="current-password">Current Password</Label>
                        <Input id="current-password" type="password" />
                      </div>
                      <div />
                      <div>
                        <Label htmlFor="new-password">New Password</Label>
                        <Input id="new-password" type="password" />
                      </div>
                      <div>
                        <Label htmlFor="confirm-password">Confirm New Password</Label>
                        <Input id="confirm-password" type="password" />
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Notifications Settings Tab */}
            <TabsContent value="notifications">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>Control how you receive notifications and updates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between py-3">
                      <div className="space-y-0.5">
                        <h4 className="text-base font-medium">Email Notifications</h4>
                        <p className="text-sm text-stone-500">Receive notifications via email</p>
                      </div>
                      <Switch
                        checked={notificationSettings.emailNotifications}
                        onCheckedChange={() => handleToggleNotification('emailNotifications')}
                      />
                    </div>
                    <div className="border-t border-stone-200" />
                    <div className="flex items-center justify-between py-3">
                      <div className="space-y-0.5">
                        <h4 className="text-base font-medium">SMS Notifications</h4>
                        <p className="text-sm text-stone-500">Receive notifications via text message</p>
                      </div>
                      <Switch
                        checked={notificationSettings.smsNotifications}
                        onCheckedChange={() => handleToggleNotification('smsNotifications')}
                      />
                    </div>
                    <div className="border-t border-stone-200" />
                    <div className="flex items-center justify-between py-3">
                      <div className="space-y-0.5">
                        <h4 className="text-base font-medium">Appointment Reminders</h4>
                        <p className="text-sm text-stone-500">Get reminders about upcoming appointments</p>
                      </div>
                      <Switch
                        checked={notificationSettings.appointmentReminders}
                        onCheckedChange={() => handleToggleNotification('appointmentReminders')}
                      />
                    </div>
                    <div className="border-t border-stone-200" />
                    <div className="flex items-center justify-between py-3">
                      <div className="space-y-0.5">
                        <h4 className="text-base font-medium">Marketing Emails</h4>
                        <p className="text-sm text-stone-500">Receive promotional content and special offers</p>
                      </div>
                      <Switch
                        checked={notificationSettings.marketingEmails}
                        onCheckedChange={() => handleToggleNotification('marketingEmails')}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button>Save Preferences</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
}