"use client";

import * as React from "react";
// import { motion } from "framer-motion";
import { Check, ChevronRight, Clock, DollarSign, Heart } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ServiceDetail {
  id: string;
  name: string;
  price: number | string;
  duration: string;
  description: string;
  benefits: string[];
  details: string;
  process: {
    title: string;
    description: string;
  }[];
  images: string[];
  popularChoice?: boolean;
}

interface ServiceDetailsProps {
  service: ServiceDetail;
  className?: string;
}

export function ServiceDetails({ service, className }: ServiceDetailsProps) {
  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-5 gap-8 p-6", className)}>
      {/* Service Gallery - 3 columns */}
      <div className="col-span-1 lg:col-span-3 space-y-4">
        <div className="relative w-full h-80 md:h-[500px] rounded-xl overflow-hidden bg-stone-50">
          <Image
            src={service.images[0]}
            alt={service.name}
            fill
            className="object-contain"
          />
          {service.popularChoice && (
            <div className="absolute top-4 right-4 bg-[#8b5d53] text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
              <Heart className="w-3 h-3 mr-1" /> Popular Choice
            </div>
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          {service.images.slice(1, 3).map((image, index) => (
            <div key={index} className="relative h-60 rounded-lg overflow-hidden bg-stone-50">
              <Image
                src={image}
                alt={`${service.name} ${index + 1}`}
                fill
                className="object-contain"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Service Details - 2 columns */}
      <div className="col-span-1 lg:col-span-2 space-y-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-serif font-light text-stone-800">{service.name}</h1>
          <div className="flex items-center space-x-4 mt-2 text-stone-600">
            <div className="flex items-center">
              <DollarSign className="w-4 h-4 mr-1 text-[#8b5d53]" />
              <span className="font-light">
                {typeof service.price === 'string' && service.price.includes('-')
                  ? `$${service.price}`
                  : `$${service.price}`}
              </span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1 text-[#8b5d53]" />
              <span className="font-light">{service.duration}</span>
            </div>
          </div>
          <p className="mt-4 text-stone-600 font-light font-cormorant">{service.description}</p>
        </div>

        <div className="space-y-3">
          <h3 className="font-serif font-light text-stone-800">Benefits</h3>
          <ul className="space-y-2">
            {service.benefits.map((benefit, index) => (
              <li key={index} className="flex text-stone-600">
                <Check className="w-5 h-5 mr-2 text-[#8b5d53] shrink-0" />
                <span className="font-light font-cormorant">{benefit}</span>
              </li>
            ))}
          </ul>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="w-full bg-stone-100">
            <TabsTrigger value="details" className="flex-1 font-serif font-light">Details</TabsTrigger>
            <TabsTrigger value="process" className="flex-1 font-serif font-light">Process</TabsTrigger>
          </TabsList>
          <TabsContent value="details" className="mt-4">
            <ScrollArea className="h-48 rounded-md border border-stone-200 p-4">
              <div className="text-stone-600 font-light font-cormorant">
                {service.details}
              </div>
            </ScrollArea>
          </TabsContent>
          <TabsContent value="process" className="mt-4">
            <ScrollArea className="h-48 rounded-md border border-stone-200 p-4">
              <div className="space-y-4">
                {service.process.map((step, index) => (
                  <div key={index} className="space-y-1">
                    <h4 className="font-serif font-light text-stone-800 flex items-center">
                      <span className="flex items-center justify-center w-5 h-5 rounded-full bg-[#8b5d53]/10 text-[#8b5d53] text-xs mr-2">
                        {index + 1}
                      </span>
                      {step.title}
                    </h4>
                    <p className="text-stone-600 font-light font-cormorant pl-7">{step.description}</p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <Button className="w-full bg-stone-800 hover:bg-stone-700 text-white font-serif font-light" size="lg" asChild>
          <Link href={`/booking?service=${service.id}`}>
            Glow Up & Book <ChevronRight className="w-4 h-4 ml-1" />
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default ServiceDetails;