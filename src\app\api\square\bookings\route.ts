import { NextResponse } from 'next/server';
import { bookingsApi, customersApi, catalogApi } from '../config';
import { serializeBigInt } from '@/lib/utils';

if (!process.env.SQUARE_STAFF_ID) {
  throw new Error('Square staff ID is not defined');
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      serviceId, 
      date, 
      time, 
      notes 
    } = body;
    
    console.log('Booking request received:', { 
      firstName, lastName, email, serviceId, date, time 
    });

    // Validate required fields
    if (!firstName || !lastName || !email || !serviceId || !date || !time) {
      console.error('Missing required fields for booking');
      return NextResponse.json(
        {
          status: 'error',
          message: 'Missing required fields'
        }, 
        { status: 400 }
      );
    }

    // Create or find customer
    try {
      // First, get the service variation details to get the version
      console.log('Fetching service variation details for ID:', serviceId);
      const { result: catalogResult } = await catalogApi.retrieveCatalogObject(serviceId, true);
      
      let serviceVariationVersion = "0"; // Default fallback version

      if (catalogResult?.object && 'version' in catalogResult.object) {
        serviceVariationVersion = String(catalogResult.object.version);
        console.log('Retrieved service variation version:', serviceVariationVersion);
      } else {
        console.warn('Could not retrieve service variation version, using default:', serviceVariationVersion);
      }

      // Find or create customer
      const { result: customerResult } = await customersApi.searchCustomers({
        query: {
          filter: {
            emailAddress: {
              exact: email
            }
          }
        }
      });

      let customerId;
      if (customerResult.customers && customerResult.customers.length > 0) {
        customerId = customerResult.customers[0].id;
        console.log('Found existing customer:', customerId);
      } else {
        console.log('Creating new customer', { firstName, lastName, email, phone });
        const { result: newCustomer } = await customersApi.createCustomer({
          idempotencyKey: `${email}-${Date.now()}`,
          givenName: firstName,
          familyName: lastName,
          emailAddress: email,
          phoneNumber: phone,
        });
        customerId = newCustomer.customer?.id;
        console.log('Created new customer:', customerId);
      }

      // Parse time correctly
      // Expected format: "10:00 AM"
      const timeParts = time.match(/(\d+):(\d+)\s*(AM|PM)/i);
      if (!timeParts) {
        console.error('Invalid time format:', time);
        return NextResponse.json({
          status: 'error',
          message: 'Invalid time format'
        }, { status: 400 });
      }

      const [, hours, minutes, period] = timeParts;
      let hourNum = parseInt(hours, 10);
      
      // Convert to 24-hour format
      if (period.toUpperCase() === 'PM' && hourNum < 12) {
        hourNum += 12;
      } else if (period.toUpperCase() === 'AM' && hourNum === 12) {
        hourNum = 0;
      }
      
      // Format time for ISO string
      const formattedTime = `${hourNum.toString().padStart(2, '0')}:${minutes}:00`;
      const startAt = new Date(`${date}T${formattedTime}`).toISOString();
      
      console.log('Creating booking with start time:', startAt);

      // Create booking
      const { result: bookingResult } = await bookingsApi.createBooking({
        idempotencyKey: `${customerId}-${date}-${time}-${Date.now()}`,
        booking: {
          startAt,
          locationId: process.env.SQUARE_LOCATION_ID!,
          customerId,
          appointmentSegments: [
            {
              serviceVariationId: serviceId,
              teamMemberId: process.env.SQUARE_STAFF_ID as string,
              serviceVariationVersion: BigInt(serviceVariationVersion)
            }
          ],
          sellerNote: notes || undefined,
        }
      });

      console.log('Booking created successfully:', bookingResult.booking?.id);

      // Create a safe booking object without BigInt values
      const safeBooking = {
        id: bookingResult.booking?.id,
        status: bookingResult.booking?.status,
        startAt: bookingResult.booking?.startAt,
        locationId: bookingResult.booking?.locationId,
        customerId: bookingResult.booking?.customerId,
        appointmentSegmentsCount: bookingResult.booking?.appointmentSegments?.length || 0
      };

      return NextResponse.json({
        status: 'success',
        booking: safeBooking
      });

    } catch (apiError: any) {
      console.error('Square API Error:', apiError);
      // Extract detailed error messages from Square API response if available
      const errorDetails = apiError.errors ? JSON.stringify(apiError.errors) : 'Unknown API error';
      return NextResponse.json({
        status: 'error',
        message: 'Failed to create booking with Square API',
        details: errorDetails
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Unexpected error in booking API:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to create booking',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 