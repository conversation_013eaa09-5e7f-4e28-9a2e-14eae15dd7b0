"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
// import { Waves } from "@/components/ui/waves-background";
import { SparklesCore } from "@/components/ui/sparkles";
import { useRouter } from 'next/navigation';
import Image from "next/image";
import { GlowButton } from "@/components/ui/glow-button";

export default function IntroPage() {
  const [hasMounted, setHasMounted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Animation variants for text and elements
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.15,
        duration: 0.7,
        ease: "easeOut"
      },
    }),
  };

  const scaleIn = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        delay: 0.3,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  if (!hasMounted) {
    return null;
  }

  return (
    <div className="relative min-h-screen w-full overflow-hidden bg-gradient-to-b from-[#faf8f7] via-[#f7f0ed] to-[#f2e7e3]">
      {/* Simplified background gradient for better mobile performance */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(139,93,83,0.08),transparent_70%)] animate-pulse-slow"></div>

      {/* Optimized particles effect for mobile */}
      <div className="absolute inset-0 opacity-15 sm:opacity-25 pointer-events-none">
        <SparklesCore
          background="transparent"
          minSize={0.5}
          maxSize={1.2}
          particleDensity={40}
          className="w-full h-full"
          particleColor="#8b5d53"
          speed={2}
        />
      </div>

      {/* Simplified gradient orbs for mobile */}
      <div className="absolute top-1/4 left-0 w-64 h-64 sm:w-80 sm:h-80 rounded-full bg-[#b07c70]/8 blur-3xl animate-float hidden sm:block"></div>
      <div className="absolute bottom-1/4 right-0 w-64 h-64 sm:w-96 sm:h-96 rounded-full bg-[#8b5d53]/8 blur-3xl animate-float-delay hidden sm:block"></div>
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] rounded-full bg-[#d4b2a7]/8 blur-3xl animate-pulse-slow"></div>

      {/* Content container - optimized for viewport fit */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen max-h-screen px-4 sm:px-6 py-4 sm:py-6">
        <div className="w-full max-w-5xl mx-auto text-center -mt-8 sm:-mt-12 md:-mt-16">
          {/* Main content */}
          <motion.div
            initial="hidden"
            animate="visible"
            className="flex flex-col items-center"
          >
            {/* Logo container with balanced positioning */}
            <motion.div
              variants={scaleIn}
              className="flex flex-col items-center relative w-full mb-0 sm:mb-1 mt-0 sm:mt-0"
            >
              {/* Enhanced background effects for the logo */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[220%] sm:w-[200%] h-[220%] sm:h-[200%] rounded-full bg-gradient-to-r from-[#d4b2a7]/3 via-[#8b5d53]/6 to-[#d4b2a7]/3 blur-3xl animate-pulse-slow"></div>
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[180%] sm:w-[160%] h-[180%] sm:h-[160%] rounded-full bg-gradient-to-br from-[#b07c70]/6 via-[#8b5d53]/12 to-[#b07c70]/6 blur-2xl animate-float-delay"></div>
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[150%] sm:w-[140%] h-[150%] sm:h-[140%] rounded-full bg-gradient-to-tr from-[#8b5d53]/8 via-[#b07c70]/15 to-[#8b5d53]/8 blur-xl animate-float"></div>

              {/* Logo image container - kept large */}
              <div className="relative z-10 w-full max-w-[520px] sm:max-w-[700px] md:max-w-[800px] h-[380px] sm:h-[520px] md:h-[600px]">
                <div className="relative h-full">
                  <Image
                    src="/glow-by-bry-logo.svg"
                    alt="GlowByBry Logo"
                    fill
                    className="object-contain"
                    priority
                    style={{
                      objectFit: 'contain',
                      filter: 'drop-shadow(0 20px 30px rgba(139,93,83,0.25))'
                    }}
                  />
                </div>
              </div>
            </motion.div>

            {/* Tagline */}
            <motion.div
              custom={1}
              variants={fadeInUp}
              className="relative mb-6 sm:mb-8 md:mb-10 w-full flex items-center justify-center -mt-20 sm:-mt-28 md:-mt-32"
            >
              <div className="relative inline-block text-center">
                <p className="text-xl sm:text-2xl md:text-3xl font-light text-[#5d3f39] max-w-xs sm:max-w-md mx-auto text-center tracking-wide uppercase font-serif sm:-translate-x-2">
                  Your skin&apos;s glow-up<br />and your soul&apos;s reset
                </p>
                {/* Enhanced decorative line */}
                <div className="absolute -bottom-3 sm:-bottom-4 left-1/2 -translate-x-1/2 w-40 sm:w-48">
                  <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-[#8b5d53]/40 to-transparent"></div>
                  <div className="h-[1px] w-3/4 mx-auto mt-1 bg-gradient-to-r from-transparent via-[#8b5d53]/30 to-transparent"></div>
                </div>
              </div>
            </motion.div>

            {/* CTA Button */}
            <motion.div
              custom={3}
              variants={fadeInUp}
              className="relative z-20 w-full flex justify-center items-center mt-2 sm:mt-4 mb-6 sm:mb-8"
            >
              <div className="w-[220px] sm:w-[240px] relative group mx-auto sm:-translate-x-4">
                {/* Enhanced glow effects */}
                <div className="absolute -inset-1 bg-gradient-to-r from-[#d4b2a7]/40 via-[#8b5d53]/60 to-[#d4b2a7]/40 rounded-full blur-md opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>
                <div className="absolute -inset-3 bg-gradient-to-r from-[#8b5d53]/10 via-[#d4b2a7]/20 to-[#8b5d53]/10 rounded-full blur-lg animate-pulse-slow"></div>
                <div className="absolute -inset-6 bg-gradient-to-tr from-[#d4b2a7]/5 via-[#8b5d53]/10 to-[#d4b2a7]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Button content */}
                <GlowButton onClick={() => router.push('/home')} className="w-full relative">
                  <div className="flex items-center justify-center py-2.5 sm:py-3 px-4 sm:px-5">
                    <span className="text-sm sm:text-base whitespace-nowrap font-light tracking-wider text-white">
                      Begin Your Glow Journey
                    </span>
                  </div>
                </GlowButton>
              </div>
            </motion.div>

            {/* Bottom navigation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 1.5 }}
              className="flex justify-center items-center gap-4 sm:gap-5 text-sm sm:text-base text-[#5d3f39]/90 sm:-translate-x-2 mt-2"
            >
              <span>Skincare</span>
              <span className="w-1.5 h-1.5 rounded-full bg-[#8b5d53]/60"></span>
              <span>Relaxation</span>
              <span className="w-1.5 h-1.5 rounded-full bg-[#8b5d53]/60"></span>
              <span>Wellness</span>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
