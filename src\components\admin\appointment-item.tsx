"use client";

import React from "react";
import { Edit, MoreH<PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
// import { Button } from "@/components/ui/button";

interface AppointmentItemProps {
  id: string;
  clientName: string;
  clientImage: string;
  service: string;
  date: string;
  time: string;
  duration: string;
  status: "confirmed" | "completed" | "cancelled" | "no-show";
  price: string;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  className?: string;
}

export function AppointmentItem({
  id,
  clientName,
  clientImage,
  service,
  date,
  time,
  duration,
  status,
  price,
  onEdit,
  onDelete,
  className = "",
}: AppointmentItemProps) {
  const [showActions, setShowActions] = React.useState(false);

  const getStatusStyles = () => {
    switch (status) {
      case "confirmed":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "no-show":
        return "bg-stone-100 text-stone-800";
      default:
        return "bg-stone-100 text-stone-800";
    }
  };

  const formatStatus = (status: string) => {
    return status.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className={`p-4 hover:bg-stone-50 transition-colors ${className}`}>
      <div className="flex items-center">
        <div className="h-10 w-10 flex-shrink-0">
          <Image
            width={40}
            height={40}
            className="rounded-full object-cover"
            src={clientImage}
            alt={clientName}
          />
        </div>
        <div className="ml-4 flex-1">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <div className="text-sm font-medium text-stone-900">{clientName}</div>
              <div className="text-sm text-stone-500">{service} ({duration})</div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-right">
                <div className="text-sm font-medium text-stone-900">{date}</div>
                <div className="text-sm text-stone-500">{time}</div>
              </div>
              <Badge className={getStatusStyles()}>
                {formatStatus(status)}
              </Badge>
              <div className="text-sm font-semibold text-stone-900">{price}</div>

              <div className="relative">
                <button
                  className="p-1 rounded-full text-stone-400 hover:text-stone-600 hover:bg-stone-100"
                  onClick={() => setShowActions(!showActions)}
                  aria-label="Appointment actions"
                >
                  <MoreHorizontal className="h-5 w-5" />
                </button>

                {showActions && (
                  <div className="absolute right-0 top-8 z-10 bg-white shadow-md rounded-md border border-stone-200 py-1 w-32">
                    <button
                      className="flex items-center w-full px-3 py-2 text-sm text-stone-700 hover:bg-stone-100"
                      onClick={() => {
                        onEdit?.(id);
                        setShowActions(false);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" /> Edit
                    </button>
                    <button
                      className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-stone-100"
                      onClick={() => {
                        onDelete?.(id);
                        setShowActions(false);
                      }}
                    >
                      <Trash className="h-4 w-4 mr-2" /> Delete
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}