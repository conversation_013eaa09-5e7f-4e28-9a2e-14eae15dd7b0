// Google Reviews Configuration
// Configure your Google Places API and business information here

export const reviewsConfig = {
  // Google Places API Configuration
  google: {
    // Get your API key from: https://console.cloud.google.com/apis/credentials
    apiKey: process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY || '',
    
    // Your Google Business Profile Place ID for Beauty Health and Wellness
    // 77935 Calle Tampico #103, La Quinta, CA 92253, USA
    placeId: 'EjI3NzkzNSBDYWxsZSBUYW1waWNvICMxMDMsIExhIFF1aW50YSwgQ0EgOTIyNTMsIFVTQSIfGh0KFgoUChIJVwMFN7z42oARYiORN8FjBWMSAzEwMw',
    
    // Your business information
    businessName: 'Beauty Health and Wellness',
    location: 'La Quinta, CA', // Updated to correct location
    
    // Google Business Profile URL (optional)
    businessUrl: '', // e.g., 'https://www.google.com/maps/place/Your+Business'
  },
  
  // Yelp Configuration (already integrated via Elfsight)
  yelp: {
    // Elfsight widget ID for Yelp reviews
    elfsightWidgetId: '8a2a1bb6-4be5-4bae-9408-f3a1690fc11a',
    
    // Your Yelp business URL (optional)
    businessUrl: '', // e.g., 'https://www.yelp.com/biz/your-business'
  },
  
  // Display settings
  display: {
    // Number of reviews to show
    maxReviews: {
      desktop: 3,
      mobile: 2
    },
    
    // Show review source badges
    showSourceBadges: true,
    
    // Show review dates
    showDates: true,
    
    // Minimum rating to display (1-5)
    minRating: 1,
  },
  
  // Filtering settings
  filtering: {
    // Only show reviews that mention these keywords (case-insensitive)
    requiredKeywords: ['bryanna', 'bry'],
    
    // Exclude reviews that mention these keywords (case-insensitive)
    excludeKeywords: ['wesley', 'gracie', 'grace', 'wes', 'amanda'],
    
    // Enable keyword filtering
    enableKeywordFiltering: true, // Only show reviews mentioning Bryanna/Bry
    
    // Enable exclusion filtering
    enableExclusionFiltering: true, // Exclude reviews mentioning other practitioners
    
    // Fallback behavior when no filtered reviews are found
    fallbackToAll: false, // Don't show other reviews if none mention Bryanna
    
    // Strict mode - only show reviews that explicitly mention Bryanna or her services
    strictMode: false, // More inclusive filtering to capture more Bryanna reviews
  },
  
  // API settings
  api: {
    // Always try to use real Google API when credentials are available
    forceRealAPI: true,
    
    // Timeout for API requests (in milliseconds)
    timeout: 10000,
  },
  
  // Fallback settings
  fallback: {
    // Use local testimonials when API fails
    useLocalTestimonials: true,
    
    // Show loading state
    showLoadingState: true,
  }
};

// Helper function to check if Google reviews are properly configured
export function isGoogleReviewsConfigured(): boolean {
  return !!(reviewsConfig.google.apiKey && reviewsConfig.google.placeId);
}

// Helper function to get review configuration for a specific source
export function getReviewConfig(source: 'google' | 'yelp') {
  return reviewsConfig[source];
}

// Helper function to filter reviews by keywords
export function filterReviewsByKeywords(reviews: any[], keywords: string[]): any[] {
  console.log(`🔍 Filtering ${reviews.length} reviews with keywords:`, keywords);
  console.log('📝 Original reviews:', reviews.map(r => ({ name: r.name, content: r.content.substring(0, 100) + '...' })));
  
  if (!reviewsConfig.filtering.enableKeywordFiltering || keywords.length === 0) {
    console.log('✅ Keyword filtering disabled or no keywords - returning all reviews');
    return reviews;
  }
  
  const filtered = reviews.filter((review, index) => {
    const content = review.content.toLowerCase();
    const name = review.name.toLowerCase();
    
    // Check if any required keyword is mentioned in the review content or reviewer name
    const matchedKeywords = keywords.filter(keyword => 
      content.includes(keyword.toLowerCase()) || 
      name.includes(keyword.toLowerCase())
    );
    
    const hasRequiredMatch = matchedKeywords.length > 0;
    
    // Check if any excluded keywords are mentioned (if exclusion filtering is enabled)
    let hasExcludedMatch = false;
    if (reviewsConfig.filtering.enableExclusionFiltering && reviewsConfig.filtering.excludeKeywords) {
      const excludedMatches = reviewsConfig.filtering.excludeKeywords.filter(keyword => 
        content.includes(keyword.toLowerCase()) || 
        name.includes(keyword.toLowerCase())
      );
      hasExcludedMatch = excludedMatches.length > 0;
      
      if (hasExcludedMatch) {
        console.log(`📋 Review ${index + 1} (${review.name}): ❌ EXCLUDED - Contains excluded keywords: [${excludedMatches.join(', ')}]`);
        return false;
      }
    }
    
    console.log(`📋 Review ${index + 1} (${review.name}): ${hasRequiredMatch ? '✅ INCLUDED' : '❌ FILTERED'} - Matched keywords: [${matchedKeywords.join(', ')}]`);
    
    return hasRequiredMatch;
  });
  
  console.log(`🎯 Filtered results: ${filtered.length}/${reviews.length} reviews passed the filter`);
  
  // Return filtered reviews, or all reviews if fallback is enabled and no matches found
  if (filtered.length === 0 && reviewsConfig.filtering.fallbackToAll) {
    console.log('🔄 No filtered reviews found, falling back to all reviews');
    return reviews;
  }
  
  return filtered;
}

// Environment variables you need to set:
// NEXT_PUBLIC_GOOGLE_PLACES_API_KEY=your_google_places_api_key
// NEXT_PUBLIC_GOOGLE_PLACE_ID=EjI3NzkzNSBDYWxsZSBUYW1waWNvICMxMDMsIExhIFF1aW50YSwgQ0EgOTIyNTMsIFVTQSIfGh0KFgoUChIJVwMFN7z42oARYiORN8FjBWMSAzEwMw 